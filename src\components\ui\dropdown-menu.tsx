'use client'

import * as React from 'react'
import Link from 'next/link'
import { cn } from '@/lib/utils'

interface DropdownMenuProps {
  trigger: React.ReactNode
  children: React.ReactNode
  className?: string
  isOpen?: boolean
  onToggle?: () => void
}

interface DropdownMenuItemProps {
  href?: string
  onClick?: () => void
  children: React.ReactNode
  className?: string
  disabled?: boolean
}

interface DropdownMenuSeparatorProps {
  className?: string
}

/**
 * Dropdown Menu Component
 *
 * A simple dropdown menu implementation with click behavior
 * Supports both link items and click handlers
 */
export function DropdownMenu({ trigger, children, className, isOpen = false, onToggle }: DropdownMenuProps) {
  const dropdownRef = React.useRef<HTMLDivElement>(null)

  // <PERSON>le clicks outside the dropdown to close it
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        if (isOpen && onToggle) {
          onToggle()
        }
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen, onToggle])

  return (
    <div
      ref={dropdownRef}
      className={cn("relative", className)}
    >
      {trigger}

      {isOpen && (
        <div className="absolute left-0 top-full mt-1 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-50">
          <div className="py-1">
            {children}
          </div>
        </div>
      )}
    </div>
  )
}

export function DropdownMenuItem({ 
  href, 
  onClick, 
  children, 
  className, 
  disabled = false 
}: DropdownMenuItemProps) {
  const baseClasses = cn(
    "flex items-center px-4 py-2 text-sm transition-colors",
    disabled 
      ? "text-gray-400 dark:text-gray-600 cursor-not-allowed"
      : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white",
    className
  )

  if (href && !disabled) {
    return (
      <Link href={href} className={baseClasses}>
        {children}
      </Link>
    )
  }

  return (
    <button
      onClick={disabled ? undefined : onClick}
      className={cn(baseClasses, "w-full text-left")}
      disabled={disabled}
    >
      {children}
    </button>
  )
}

export function DropdownMenuSeparator({ className }: DropdownMenuSeparatorProps) {
  return (
    <div className={cn("h-px bg-gray-200 dark:bg-gray-700 my-1", className)} />
  )
}
