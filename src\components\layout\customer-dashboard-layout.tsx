'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/hooks/use-auth'
import { cn } from '@/lib/utils'
import { usePackageCounts } from '@/hooks/use-package-counts'
import { useShipmentCounts } from '@/hooks/use-shipment-counts'
import {
  Package,
  Calculator,
  MapPin,
  CreditCard,
  Settings,
  LogOut,
  Menu,
  X,
  Home,
  TrendingUp,
  Bell,
  HelpCircle,
  Star,
  ChevronRight,
  ChevronDown,
  Send,
  Archive,
  Combine,
  Truck,
  RotateCcw,
  Trash2,
  FileText,
  Plus,
  Clock,
  CheckCircle,
  Package2,
  XCircle
} from 'lucide-react'

interface CustomerDashboardLayoutProps {
  children: React.ReactNode
}

// Customer-focused navigation items (excluding My Packages and Request Shipment which have their own dropdowns)
const customerNavigation = [
  { name: 'Dashboard', href: '/dashboard', icon: Home },
  // My Packages dropdown will be inserted here as second item
  // Request Shipment dropdown will be inserted here as third item
  { name: 'Track Packages', href: '/packages/track', icon: Package },
  { name: 'Shipping Calculator', href: '/shipping', icon: Calculator },
  { name: 'Address Book', href: '/addresses', icon: MapPin },
  { name: 'Billing & Payments', href: '/billing', icon: CreditCard },
  { name: 'Upgrade Plan', href: '/dashboard/upgrade', icon: TrendingUp },
  { name: 'Support', href: '/support', icon: HelpCircle },
  { name: 'Settings', href: '/settings', icon: Settings },
]

/**
 * Customer Dashboard Layout
 * 
 * Optimized for end users who use the forwarding service
 * Features:
 * - Package management and tracking
 * - Shipping calculator and tools
 * - Address book management
 * - Billing and plan upgrade options
 * - Customer support access
 */
export default function CustomerDashboardLayout({ children }: CustomerDashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { user, logout } = useAuth()
  const pathname = usePathname()
  const [expandedMenu, setExpandedMenu] = useState<'packages' | 'shipments' | null>(null)
  const packageCounts = usePackageCounts()
  const shipmentCounts = useShipmentCounts()

  const handleMenuToggle = (menu: 'packages' | 'shipments') => {
    setExpandedMenu(expandedMenu === menu ? null : menu)
  }

  const closeMenus = () => {
    setExpandedMenu(null)
  }

  // Package sub-menu items
  const packageSubMenuItems = [
    { name: 'View All Packages', href: '/packages', icon: Package, count: packageCounts.total },
    { name: 'Packages in Account', href: '/packages?status=in-account', icon: Archive, count: packageCounts.inAccount, color: 'blue' },
    { name: 'Consolidations', href: '/packages/consolidations', icon: Combine, count: packageCounts.consolidations, color: 'purple' },
    { name: 'Ready for Mailout', href: '/packages?status=ready-for-mailout', icon: Send, count: packageCounts.readyForMailout, color: 'green' },
    { name: 'Sent', href: '/packages?status=sent', icon: Truck, count: packageCounts.sent, color: 'orange' },
    { name: 'Return', href: '/packages/returns', icon: RotateCcw, count: packageCounts.returns, color: 'yellow' },
    { name: 'Trashed', href: '/packages/trashed', icon: Trash2, count: packageCounts.trashed, color: 'red' }
  ]

  // Shipment sub-menu items
  const shipmentSubMenuItems = [
    { name: 'View All Shipments', href: '/shipments', icon: FileText, count: shipmentCounts.total },
    { name: 'Request Shipment', href: '/shipments/request', icon: Plus, color: 'green' },
    { name: 'Under Review', href: '/shipments?status=under-review', icon: Clock, count: shipmentCounts.underReview, color: 'yellow' },
    { name: 'Approved', href: '/shipments?status=approved', icon: CheckCircle, count: shipmentCounts.approved, color: 'blue' },
    { name: 'Ready to Send', href: '/shipments?status=ready-to-send', icon: Truck, count: shipmentCounts.readyToSend, color: 'purple' },
    { name: 'Completed', href: '/shipments?status=completed', icon: Package2, count: shipmentCounts.completed, color: 'green' },
    { name: 'Cancelled', href: '/shipments?status=cancelled', icon: XCircle, count: shipmentCounts.cancelled, color: 'red' }
  ]

  // Helper function to get counter badge color
  const getCounterColor = (color?: string) => {
    const colors = {
      blue: 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200',
      purple: 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200',
      green: 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',
      orange: 'bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200',
      yellow: 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200',
      red: 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
    }
    return color ? colors[color as keyof typeof colors] : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
  }

  // Helper function to render sub-menu items
  const renderSubMenuItem = (item: any, onItemClick?: () => void) => {
    const Icon = item.icon
    const isActive = pathname === item.href || (item.href.includes('?') && pathname.startsWith(item.href.split('?')[0]) && pathname.includes(item.href.split('?')[1]))

    return (
      <Link
        key={item.name}
        href={item.href}
        className={cn(
          "flex items-center pl-12 pr-2 py-2 text-sm font-medium rounded-md transition-colors",
          isActive
            ? "bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100"
            : "text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"
        )}
        onClick={onItemClick}
      >
        <Icon className="mr-3 h-4 w-4" />
        <span className="flex-1">{item.name}</span>
        {item.count !== undefined && item.count > 0 && (
          <span className={cn(
            "ml-auto text-xs px-2 py-1 rounded-full font-medium",
            getCounterColor(item.color)
          )}>
            {item.count}
          </span>
        )}
      </Link>
    )
  }

  const handleSignOut = async () => {
    await logout()
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Mobile sidebar */}
      <div className={cn(
        "fixed inset-0 z-50 lg:hidden",
        sidebarOpen ? "block" : "hidden"
      )}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-white dark:bg-gray-800 shadow-xl">
          <div className="flex h-16 items-center justify-between px-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">L</span>
              </div>
              <span className="text-xl font-bold text-gray-900 dark:text-white">LibyanoEx</span>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setSidebarOpen(false)}
            >
              <X className="h-6 w-6" />
            </Button>
          </div>
          <nav className="flex-1 space-y-1 px-2 py-4">
            {customerNavigation.map((item, index) => {
              const Icon = item.icon
              const isActive = pathname === item.href

              return (
                <div key={item.name}>
                  <Link
                    href={item.href}
                    className={cn(
                      "group flex items-center px-2 py-2 text-sm font-medium rounded-md",
                      isActive
                        ? "bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100"
                        : "text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"
                    )}
                    onClick={() => {
                      setSidebarOpen(false)
                      closeMenus()
                    }}
                  >
                    <Icon className="mr-3 h-5 w-5" />
                    {item.name}
                  </Link>

                  {/* Insert My Packages and Request Shipment sub-menus after Dashboard (index 0) */}
                  {index === 0 && (
                    <div className="space-y-1">
                      {/* My Packages Sub-Menu */}
                      <div>
                        <div
                          className={cn(
                            "flex items-center px-2 py-2 text-sm font-medium rounded-md cursor-pointer",
                            pathname.startsWith('/packages') && pathname !== '/packages/track'
                              ? "bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100"
                              : "text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"
                          )}
                          onClick={() => handleMenuToggle('packages')}
                        >
                          <Package className="mr-3 h-5 w-5" />
                          <span className="flex-1">My Packages</span>
                          {expandedMenu === 'packages' ? (
                            <ChevronDown className="ml-1 h-4 w-4" />
                          ) : (
                            <ChevronRight className="ml-1 h-4 w-4" />
                          )}
                        </div>
                        {expandedMenu === 'packages' && (
                          <div className="space-y-1 mt-1">
                            {packageSubMenuItems.map(item =>
                              renderSubMenuItem(item, () => setSidebarOpen(false))
                            )}
                          </div>
                        )}
                      </div>

                      {/* Request Shipment Sub-Menu */}
                      <div>
                        <div
                          className={cn(
                            "flex items-center px-2 py-2 text-sm font-medium rounded-md cursor-pointer",
                            pathname.startsWith('/shipments')
                              ? "bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100"
                              : "text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"
                          )}
                          onClick={() => handleMenuToggle('shipments')}
                        >
                          <Send className="mr-3 h-5 w-5" />
                          <span className="flex-1">Request Shipment</span>
                          {expandedMenu === 'shipments' ? (
                            <ChevronDown className="ml-1 h-4 w-4" />
                          ) : (
                            <ChevronRight className="ml-1 h-4 w-4" />
                          )}
                        </div>
                        {expandedMenu === 'shipments' && (
                          <div className="space-y-1 mt-1">
                            {shipmentSubMenuItems.map(item =>
                              renderSubMenuItem(item, () => setSidebarOpen(false))
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </nav>
          
          {/* Customer Plan Badge */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-2 p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <Star className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-900 dark:text-green-100">Basic Plan</span>
            </div>
          </div>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-col flex-grow bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
          <div className="flex h-16 items-center px-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">L</span>
              </div>
              <span className="text-xl font-bold text-gray-900 dark:text-white">LibyanoEx</span>
            </div>
          </div>
          <nav className="flex-1 space-y-1 px-2 py-4">
            {customerNavigation.map((item, index) => {
              const Icon = item.icon
              const isActive = pathname === item.href

              return (
                <div key={item.name}>
                  <Link
                    href={item.href}
                    className={cn(
                      "group flex items-center px-2 py-2 text-sm font-medium rounded-md",
                      isActive
                        ? "bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100"
                        : "text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"
                    )}
                    onClick={closeMenus}
                  >
                    <Icon className="mr-3 h-5 w-5" />
                    {item.name}
                  </Link>

                  {/* Insert My Packages and Request Shipment sub-menus after Dashboard (index 0) */}
                  {index === 0 && (
                    <div className="space-y-1">
                      {/* My Packages Sub-Menu */}
                      <div>
                        <div
                          className={cn(
                            "flex items-center px-2 py-2 text-sm font-medium rounded-md cursor-pointer",
                            pathname.startsWith('/packages') && pathname !== '/packages/track'
                              ? "bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100"
                              : "text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"
                          )}
                          onClick={() => handleMenuToggle('packages')}
                        >
                          <Package className="mr-3 h-5 w-5" />
                          <span className="flex-1">My Packages</span>
                          {expandedMenu === 'packages' ? (
                            <ChevronDown className="ml-1 h-4 w-4" />
                          ) : (
                            <ChevronRight className="ml-1 h-4 w-4" />
                          )}
                        </div>
                        {expandedMenu === 'packages' && (
                          <div className="space-y-1 mt-1">
                            {packageSubMenuItems.map(item =>
                              renderSubMenuItem(item)
                            )}
                          </div>
                        )}
                      </div>

                      {/* Request Shipment Sub-Menu */}
                      <div>
                        <div
                          className={cn(
                            "flex items-center px-2 py-2 text-sm font-medium rounded-md cursor-pointer",
                            pathname.startsWith('/shipments')
                              ? "bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100"
                              : "text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"
                          )}
                          onClick={() => handleMenuToggle('shipments')}
                        >
                          <Send className="mr-3 h-5 w-5" />
                          <span className="flex-1">Request Shipment</span>
                          {expandedMenu === 'shipments' ? (
                            <ChevronDown className="ml-1 h-4 w-4" />
                          ) : (
                            <ChevronRight className="ml-1 h-4 w-4" />
                          )}
                        </div>
                        {expandedMenu === 'shipments' && (
                          <div className="space-y-1 mt-1">
                            {shipmentSubMenuItems.map(item =>
                              renderSubMenuItem(item)
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </nav>
          
          {/* Customer Plan Badge */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-2 p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <Star className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-900 dark:text-green-100">Basic Plan</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-40 flex h-16 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm dark:border-gray-700 dark:bg-gray-800 sm:gap-x-6 sm:px-6 lg:px-8">
          <Button
            variant="ghost"
            size="icon"
            className="lg:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </Button>

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex flex-1" />
            <div className="flex items-center gap-x-4 lg:gap-x-6">
              {/* Notifications */}
              <Button variant="ghost" size="icon" title="Notifications">
                <Bell className="h-5 w-5" />
              </Button>
              
              {/* User menu */}
              <div className="flex items-center space-x-4">
                <div className="text-sm">
                  <p className="font-medium text-gray-900 dark:text-white">
                    {user?.full_name || user?.email}
                  </p>
                  <p className="text-gray-500 dark:text-gray-400 capitalize">
                    Customer
                  </p>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleSignOut}
                  title="Sign out"
                >
                  <LogOut className="h-5 w-5" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="py-6">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
