'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/hooks/use-auth'
import { cn } from '@/lib/utils'
import PackagesDropdown from '@/components/navigation/packages-dropdown'
import ShipmentsDropdown from '@/components/navigation/shipments-dropdown'
import {
  Package,
  Calculator,
  MapPin,
  CreditCard,
  Settings,
  LogOut,
  Menu,
  X,
  Home,
  TrendingUp,
  Bell,
  HelpCircle,
  Star
} from 'lucide-react'

interface CustomerDashboardLayoutProps {
  children: React.ReactNode
}

// Customer-focused navigation items (excluding My Packages and Request Shipment which have their own dropdowns)
const customerNavigation = [
  { name: 'Dashboard', href: '/dashboard', icon: Home },
  // My Packages dropdown will be inserted here as second item
  // Request Shipment dropdown will be inserted here as third item
  { name: 'Track Packages', href: '/packages/track', icon: Package },
  { name: 'Shipping Calculator', href: '/shipping', icon: Calculator },
  { name: 'Address Book', href: '/addresses', icon: MapPin },
  { name: 'Billing & Payments', href: '/billing', icon: CreditCard },
  { name: 'Upgrade Plan', href: '/dashboard/upgrade', icon: TrendingUp },
  { name: 'Support', href: '/support', icon: HelpCircle },
  { name: 'Settings', href: '/settings', icon: Settings },
]

/**
 * Customer Dashboard Layout
 * 
 * Optimized for end users who use the forwarding service
 * Features:
 * - Package management and tracking
 * - Shipping calculator and tools
 * - Address book management
 * - Billing and plan upgrade options
 * - Customer support access
 */
export default function CustomerDashboardLayout({ children }: CustomerDashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { user, logout } = useAuth()
  const pathname = usePathname()

  const handleSignOut = async () => {
    await logout()
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Mobile sidebar */}
      <div className={cn(
        "fixed inset-0 z-50 lg:hidden",
        sidebarOpen ? "block" : "hidden"
      )}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-white dark:bg-gray-800 shadow-xl">
          <div className="flex h-16 items-center justify-between px-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">L</span>
              </div>
              <span className="text-xl font-bold text-gray-900 dark:text-white">LibyanoEx</span>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setSidebarOpen(false)}
            >
              <X className="h-6 w-6" />
            </Button>
          </div>
          <nav className="flex-1 space-y-1 px-2 py-4">
            {customerNavigation.map((item, index) => {
              const Icon = item.icon
              const isActive = pathname === item.href

              return (
                <div key={item.name}>
                  <Link
                    href={item.href}
                    className={cn(
                      "group flex items-center px-2 py-2 text-sm font-medium rounded-md",
                      isActive
                        ? "bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100"
                        : "text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"
                    )}
                    onClick={() => setSidebarOpen(false)}
                  >
                    <Icon className="mr-3 h-5 w-5" />
                    {item.name}
                  </Link>

                  {/* Insert My Packages Dropdown after Dashboard (index 0) */}
                  {index === 0 && (
                    <div className="space-y-1">
                      <div className="px-2 mt-1">
                        <PackagesDropdown
                          isActive={pathname.startsWith('/packages') && pathname !== '/packages/track'}
                        />
                      </div>
                      <div className="px-2">
                        <ShipmentsDropdown
                          isActive={pathname.startsWith('/shipments')}
                        />
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </nav>
          
          {/* Customer Plan Badge */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-2 p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <Star className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-900 dark:text-green-100">Basic Plan</span>
            </div>
          </div>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-col flex-grow bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
          <div className="flex h-16 items-center px-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">L</span>
              </div>
              <span className="text-xl font-bold text-gray-900 dark:text-white">LibyanoEx</span>
            </div>
          </div>
          <nav className="flex-1 space-y-1 px-2 py-4">
            {customerNavigation.map((item, index) => {
              const Icon = item.icon
              const isActive = pathname === item.href

              return (
                <div key={item.name}>
                  <Link
                    href={item.href}
                    className={cn(
                      "group flex items-center px-2 py-2 text-sm font-medium rounded-md",
                      isActive
                        ? "bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100"
                        : "text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white"
                    )}
                  >
                    <Icon className="mr-3 h-5 w-5" />
                    {item.name}
                  </Link>

                  {/* Insert My Packages and Request Shipment Dropdowns after Dashboard (index 0) */}
                  {index === 0 && (
                    <div className="space-y-1">
                      <div className="px-2 mt-1">
                        <PackagesDropdown
                          isActive={pathname.startsWith('/packages') && pathname !== '/packages/track'}
                        />
                      </div>
                      <div className="px-2">
                        <ShipmentsDropdown
                          isActive={pathname.startsWith('/shipments')}
                        />
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </nav>
          
          {/* Customer Plan Badge */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-2 p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <Star className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-900 dark:text-green-100">Basic Plan</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-40 flex h-16 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm dark:border-gray-700 dark:bg-gray-800 sm:gap-x-6 sm:px-6 lg:px-8">
          <Button
            variant="ghost"
            size="icon"
            className="lg:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </Button>

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex flex-1" />
            <div className="flex items-center gap-x-4 lg:gap-x-6">
              {/* Notifications */}
              <Button variant="ghost" size="icon" title="Notifications">
                <Bell className="h-5 w-5" />
              </Button>
              
              {/* User menu */}
              <div className="flex items-center space-x-4">
                <div className="text-sm">
                  <p className="font-medium text-gray-900 dark:text-white">
                    {user?.full_name || user?.email}
                  </p>
                  <p className="text-gray-500 dark:text-gray-400 capitalize">
                    Customer
                  </p>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleSignOut}
                  title="Sign out"
                >
                  <LogOut className="h-5 w-5" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="py-6">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
