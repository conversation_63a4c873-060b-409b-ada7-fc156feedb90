/**
 * Home Page - Shipping Portal Authentication Landing
 *
 * This is the main entry point for users coming from libyanoex.com
 * Provides options to sign in or register for the shipping forwarding portal
 *
 * Features:
 * - Clean, professional design matching shipping company branding
 * - Clear call-to-action buttons for login/register
 * - Shipping-focused value proposition and features
 * - Responsive design optimized for all devices
 * - Links back to main company website
 */

import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Package, Shield, Clock, Globe, Truck, MapPin } from "lucide-react"
import GuestTracking from "@/components/guest-tracking"
import ClientWrapper from "@/components/client-wrapper"

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">


      {/* Hero Section */}
      <section className="container mx-auto px-4 py-20 text-center">
        <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
          Welcome to Your
          <span className="text-blue-600"> Shipping Portal</span>
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
          Manage your packages, track shipments, and access your LibyanoEx forwarding account
          from anywhere in the world. Your trusted international shipping partner.
        </p>

        {/* Primary Authentication Actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
          <Link href="/auth/login">
            <Button size="lg" className="w-full sm:w-auto text-lg px-8 py-3">
              Sign In to Your Account
            </Button>
          </Link>
          <Link href="/auth/register">
            <Button variant="outline" size="lg" className="w-full sm:w-auto text-lg px-8 py-3">
              Create New Account
            </Button>
          </Link>
        </div>

        {/* Guest Package Tracking */}
        <div className="max-w-lg mx-auto">
          <ClientWrapper
            fallback={
              <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-2 border-blue-200 dark:border-blue-800 shadow-lg">
                <CardContent className="p-6">
                  <div className="text-center">
                    <div className="flex items-center justify-center mb-3">
                      <Package className="h-8 w-8 text-blue-600 mr-2" />
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                        Track Your Package
                      </h3>
                    </div>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Loading tracking interface...
                    </p>
                  </div>
                </CardContent>
              </Card>
            }
          >
            <GuestTracking />
          </ClientWrapper>
        </div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-4 py-20">
        <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white mb-12">
          Why Choose LibyanoEx for Your Shipping Needs?
        </h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-5xl mx-auto">
          <Card className="text-center">
            <CardHeader>
              <Package className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <CardTitle>Package Management</CardTitle>
              <CardDescription>
                Track and manage all your packages in one convenient dashboard with real-time updates
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <Clock className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <CardTitle>Fast Processing</CardTitle>
              <CardDescription>
                Quick package processing and shipping with same-day handling for urgent deliveries
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <Globe className="h-12 w-12 text-purple-600 mx-auto mb-4" />
              <CardTitle>Global Shipping</CardTitle>
              <CardDescription>
                Ship anywhere in the world with our extensive network of trusted shipping partners
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <Shield className="h-12 w-12 text-red-600 mx-auto mb-4" />
              <CardTitle>Secure & Insured</CardTitle>
              <CardDescription>
                Your packages are protected with comprehensive insurance and secure handling
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <Truck className="h-12 w-12 text-orange-600 mx-auto mb-4" />
              <CardTitle>Multiple Carriers</CardTitle>
              <CardDescription>
                Choose from DHL, FedEx, UPS, and more to find the best rates and delivery times
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <MapPin className="h-12 w-12 text-indigo-600 mx-auto mb-4" />
              <CardTitle>Address Management</CardTitle>
              <CardDescription>
                Manage multiple shipping addresses and set defaults for quick and easy ordering
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="container mx-auto px-4 py-16">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-lg text-center max-w-3xl mx-auto">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Ready to Start Shipping?
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mb-6 text-lg">
            Join thousands of satisfied customers who trust LibyanoEx for their international shipping needs.
            Get started today with your free account.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth/register">
              <Button size="lg" className="w-full sm:w-auto text-lg px-8 py-3">
                Create Your Account
              </Button>
            </Link>
            <Link href="/auth/login">
              <Button variant="outline" size="lg" className="w-full sm:w-auto text-lg px-8 py-3">
                Sign In
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Compact Footer */}
      <footer className="bg-gray-900 text-white py-6">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
            <div className="flex items-center space-x-2">
              <Package className="h-6 w-6 text-blue-600" />
              <span className="text-lg font-bold">LibyanoEx</span>
            </div>
            <div className="flex flex-wrap items-center justify-center space-x-6 text-sm text-gray-400">
              <a href="https://libyanoex.com" className="hover:text-white transition-colors">Main Website</a>
              <a href="https://libyanoex.com/services" className="hover:text-white transition-colors">Services</a>
              <a href="https://libyanoex.com/contact" className="hover:text-white transition-colors">Contact</a>
              <a href="https://libyanoex.com/help" className="hover:text-white transition-colors">Help</a>
            </div>
            <div className="text-sm text-gray-400 text-center md:text-right">
              <p>&copy; 2025 LibyanoEx. All rights reserved.</p>
              <div className="flex space-x-4 mt-1 justify-center md:justify-end">
                <a href="https://libyanoex.com/privacy" className="hover:text-white transition-colors">Privacy</a>
                <a href="https://libyanoex.com/terms" className="hover:text-white transition-colors">Terms</a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
