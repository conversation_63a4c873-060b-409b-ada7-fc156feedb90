/**
 * Package Tracking Page - LibyanoEx Forwarding Service
 *
 * Comprehensive package tracking functionality similar to UPS tracking
 *
 * Features:
 * - Package tracking input with multiple search options
 * - Real-time tracking status with visual indicators
 * - Detailed shipment timeline and milestones
 * - Interactive tracking map and route visualization
 * - Package details and delivery information
 * - Multi-package tracking capability
 * - Print-friendly tracking summary
 * - Customer support integration
 */

'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import RoleBasedDashboardLayout from '@/components/layout/role-based-dashboard-layout'
import GuestLayout from '@/components/layout/guest-layout'
import { useAuth } from '@/hooks/use-auth'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import LoadingSpinner from '@/components/ui/loading-spinner'
import Breadcrumb from '@/components/ui/breadcrumb'
import TrackingStatusComponent from '@/components/tracking/tracking-status'
import TrackingTimeline from '@/components/tracking/tracking-timeline'
import PackageDetailsComponent from '@/components/tracking/package-details'
import TrackingMap from '@/components/tracking/tracking-map'
import TrackingPrintView from '@/components/tracking/tracking-print-view'
import TrackingNotifications from '@/components/tracking/tracking-notifications'
import { searchPackageTracking } from '@/lib/mock-tracking-data'
import { PackageTracking, TrackingSearchResult } from '@/types'
import {
  Search,
  Package,
  Plus,
  X,
  Printer,
  Share2,
  Phone,
  Mail,
  AlertCircle,
  CheckCircle,
  Clock,
  RefreshCw
} from 'lucide-react'

export default function PackageTrackingPage() {
  const { user, isLoading: authLoading } = useAuth()
  const searchParams = useSearchParams()
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [searchResult, setSearchResult] = useState<TrackingSearchResult | null>(null)
  const [trackedPackages, setTrackedPackages] = useState<PackageTracking[]>([])
  const [activePackageId, setActivePackageId] = useState<string | null>(null)

  // Check if this is a guest user (no authentication)
  const isGuest = !authLoading && !user

  // Handle guest tracking from URL parameters
  useEffect(() => {
    const queryParam = searchParams.get('q')
    if (queryParam) {
      setSearchQuery(queryParam)
      // Auto-search when coming from guest tracking
      handleSearchWithQuery(queryParam)
    }
  }, [searchParams])

  const handleSearchWithQuery = async (query: string) => {
    if (!query.trim()) return

    setIsLoading(true)
    try {
      const result = await searchPackageTracking(query)
      setSearchResult(result)

      if (result.success && result.package) {
        // Add to tracked packages if not already present
        const existingIndex = trackedPackages.findIndex(
          pkg => pkg.id === result.package!.id
        )

        if (existingIndex === -1) {
          setTrackedPackages(prev => [...prev, result.package!])
        } else {
          // Update existing package
          setTrackedPackages(prev =>
            prev.map((pkg, index) =>
              index === existingIndex ? result.package! : pkg
            )
          )
        }

        setActivePackageId(result.package.id)
        if (query === searchQuery) {
          setSearchQuery('')
        }
      }
    } catch (error) {
      console.error('Search error:', error)
      setSearchResult({
        success: false,
        error: 'An error occurred while searching. Please try again.'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = async () => {
    await handleSearchWithQuery(searchQuery)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  const removePackage = (packageId: string) => {
    setTrackedPackages(prev => prev.filter(pkg => pkg.id !== packageId))
    if (activePackageId === packageId) {
      setActivePackageId(trackedPackages.length > 1 ? trackedPackages[0].id : null)
    }
  }

  const activePackage = trackedPackages.find(pkg => pkg.id === activePackageId)

  const handlePrint = () => {
    window.print()
  }

  const handleShare = async () => {
    if (activePackage) {
      const shareData = {
        title: `LibyanoEx Package Tracking - ${activePackage.trackingNumber}`,
        text: `Track package ${activePackage.trackingNumber} - Status: ${activePackage.statusDescription}`,
        url: window.location.href
      }

      if (navigator.share) {
        try {
          await navigator.share(shareData)
        } catch (error) {
          // Fallback to clipboard
          navigator.clipboard.writeText(window.location.href)
          alert('Tracking link copied to clipboard!')
        }
      } else {
        navigator.clipboard.writeText(window.location.href)
        alert('Tracking link copied to clipboard!')
      }
    }
  }

  // Use appropriate layout based on authentication status
  if (isGuest) {
    return (
      <GuestLayout>
        <div className="space-y-6">
        {/* Breadcrumb Navigation - Only for authenticated users */}
        {!isGuest && (
          <Breadcrumb
            items={[
              { label: 'Packages', href: '/packages' },
              { label: 'Track Package', current: true }
            ]}
          />
        )}

        {/* Page Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Package Tracking
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {isGuest
                ? "Track your packages without logging in - enter your tracking number below"
                : "Track your packages in real-time with detailed status updates"
              }
            </p>
          </div>
          
          {/* Action Buttons */}
          {activePackage && (
            <div className="flex items-center space-x-2 mt-4 md:mt-0">
              <Button variant="outline" size="sm" onClick={handlePrint}>
                <Printer className="h-4 w-4 mr-2" />
                Print
              </Button>
              <Button variant="outline" size="sm" onClick={handleShare}>
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
            </div>
          )}
        </div>

        {/* Search Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Search className="h-5 w-5 text-blue-600" />
              <span>Track Your Package</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex space-x-2">
              <div className="flex-1">
                <Input
                  placeholder="Enter tracking number, reference number, or order ID..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="text-base"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Example: LBX123456789, REF-2024-001, or ORD-789123
                </p>
              </div>
              <Button 
                onClick={handleSearch} 
                disabled={isLoading || !searchQuery.trim()}
                className="px-6"
              >
                {isLoading ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <Search className="h-4 w-4" />
                )}
              </Button>
            </div>

            {/* Search Results */}
            {searchResult && !searchResult.success && (
              <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <div className="flex items-start space-x-2">
                  <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-red-800 dark:text-red-200">
                      {searchResult.error}
                    </p>
                    {searchResult.suggestions && (
                      <ul className="text-xs text-red-700 dark:text-red-300 mt-2 space-y-1">
                        {searchResult.suggestions.map((suggestion, index) => (
                          <li key={index}>• {suggestion}</li>
                        ))}
                      </ul>
                    )}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Tracked Packages Tabs */}
        {trackedPackages.length > 0 && (
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2 overflow-x-auto">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 whitespace-nowrap">
                  Tracking:
                </span>
                {trackedPackages.map((pkg) => (
                  <div
                    key={pkg.id}
                    className={`
                      flex items-center space-x-2 px-3 py-2 rounded-lg cursor-pointer whitespace-nowrap
                      ${activePackageId === pkg.id 
                        ? 'bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700'
                      }
                    `}
                    onClick={() => setActivePackageId(pkg.id)}
                  >
                    <Package className="h-4 w-4" />
                    <span className="text-sm font-medium">{pkg.trackingNumber}</span>
                    <TrackingStatusComponent 
                      status={pkg.status} 
                      statusDescription=""
                      showIcon={false}
                      size="sm"
                    />
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        removePackage(pkg.id)
                      }}
                      className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Package Details */}
        {activePackage ? (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Status Overview */}
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                        {activePackage.trackingNumber}
                      </h2>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Last updated: {new Date(activePackage.updatedAt).toLocaleString()}
                      </p>
                    </div>
                    <TrackingStatusComponent 
                      status={activePackage.status}
                      statusDescription={activePackage.statusDescription}
                      size="lg"
                    />
                  </div>

                  {/* Delivery Estimate */}
                  <div className="flex items-center space-x-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <Clock className="h-5 w-5 text-blue-600" />
                    <div>
                      <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
                        Estimated Delivery
                      </p>
                      <p className="text-sm text-blue-700 dark:text-blue-200">
                        {new Date(activePackage.estimatedDelivery).toLocaleDateString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                        {activePackage.deliveryInfo.estimatedTimeWindow && 
                          `, ${activePackage.deliveryInfo.estimatedTimeWindow}`
                        }
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Tracking Timeline */}
              <TrackingTimeline events={activePackage.events} />

              {/* Tracking Map */}
              <TrackingMap
                origin={activePackage.origin}
                destination={activePackage.destination}
                currentLocation={activePackage.currentLocation}
                isDelivered={activePackage.isDelivered}
              />
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Package Details */}
              <PackageDetailsComponent
                packageDetails={activePackage.packageDetails}
                deliveryInfo={activePackage.deliveryInfo}
                trackingNumber={activePackage.trackingNumber}
                referenceNumber={activePackage.referenceNumber}
                orderId={activePackage.orderId}
              />

              {/* Tracking Notifications */}
              <TrackingNotifications package={activePackage} />

              {/* Customer Support */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Phone className="h-5 w-5 text-green-600" />
                    <span>Need Help?</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Having issues with your package? Our customer support team is here to help.
                  </p>

                  <div className="space-y-2">
                    <Button variant="outline" className="w-full justify-start" size="sm">
                      <Phone className="h-4 w-4 mr-2" />
                      Call Support: +****************
                    </Button>

                    <Button variant="outline" className="w-full justify-start" size="sm">
                      <Mail className="h-4 w-4 mr-2" />
                      Email: <EMAIL>
                    </Button>
                  </div>

                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    <p><strong>Hours:</strong> Mon-Fri 8AM-8PM EST</p>
                    <p><strong>Reference:</strong> {activePackage.trackingNumber}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        ) : (
          /* Empty State */
          <Card>
            <CardContent className="p-12 text-center">
              <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {isGuest ? "Track Your Package" : "No packages to track"}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {isGuest
                  ? "Enter your tracking number above to get real-time package updates"
                  : "Enter a tracking number above to get started with package tracking"
                }
              </p>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                <p>You can track packages using:</p>
                <ul className="mt-2 space-y-1">
                  <li>• Tracking numbers (LBX123456789)</li>
                  <li>• Reference numbers (REF-2024-001)</li>
                  <li>• Order IDs (ORD-789123)</li>
                </ul>
                {isGuest && (
                  <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <p className="text-blue-700 dark:text-blue-300 text-sm">
                      💡 <strong>No account required!</strong> Track any LibyanoEx package instantly.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Print View (Hidden, only visible when printing) */}
        {activePackage && <TrackingPrintView package={activePackage} />}
        </div>
      </GuestLayout>
    )
  }

  return (
    <RoleBasedDashboardLayout>
      <div className="space-y-6">
        {/* Breadcrumb Navigation - Only for authenticated users */}
        <Breadcrumb
          items={[
            { label: 'Packages', href: '/packages' },
            { label: 'Track Package', current: true }
          ]}
        />

        {/* Page Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Package Tracking
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Track your packages in real-time with detailed status updates
            </p>
          </div>

          {/* Action Buttons */}
          {activePackage && (
            <div className="flex items-center space-x-2 mt-4 md:mt-0">
              <Button variant="outline" size="sm" onClick={handlePrint}>
                <Printer className="h-4 w-4 mr-2" />
                Print
              </Button>
              <Button variant="outline" size="sm" onClick={handleShare}>
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
            </div>
          )}
        </div>

        {/* Search Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Search className="h-5 w-5 text-blue-600" />
              <span>Track Your Package</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex space-x-2">
              <div className="flex-1">
                <Input
                  placeholder="Enter tracking number, reference number, or order ID..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="text-base"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Example: LBX123456789, REF-2024-001, or ORD-789123
                </p>
              </div>
              <Button
                onClick={handleSearch}
                disabled={isLoading || !searchQuery.trim()}
                className="px-6"
              >
                {isLoading ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <Search className="h-4 w-4" />
                )}
              </Button>
            </div>

            {/* Search Results */}
            {searchResult && !searchResult.success && (
              <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <div className="flex items-start space-x-2">
                  <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-red-800 dark:text-red-200">
                      {searchResult.error}
                    </p>
                    {searchResult.suggestions && (
                      <ul className="text-xs text-red-700 dark:text-red-300 mt-2 space-y-1">
                        {searchResult.suggestions.map((suggestion, index) => (
                          <li key={index}>• {suggestion}</li>
                        ))}
                      </ul>
                    )}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Tracked Packages Tabs */}
        {trackedPackages.length > 0 && (
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2 overflow-x-auto">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 whitespace-nowrap">
                  Tracking:
                </span>
                {trackedPackages.map((pkg) => (
                  <div
                    key={pkg.id}
                    className={`
                      flex items-center space-x-2 px-3 py-2 rounded-lg cursor-pointer whitespace-nowrap
                      ${activePackageId === pkg.id
                        ? 'bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700'
                      }
                    `}
                    onClick={() => setActivePackageId(pkg.id)}
                  >
                    <Package className="h-4 w-4" />
                    <span className="text-sm font-medium">{pkg.trackingNumber}</span>
                    <TrackingStatusComponent
                      status={pkg.status}
                      statusDescription=""
                      showIcon={false}
                      size="sm"
                    />
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        removePackage(pkg.id)
                      }}
                      className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Package Details */}
        {activePackage ? (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Status Overview */}
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                        {activePackage.trackingNumber}
                      </h2>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Last updated: {new Date(activePackage.updatedAt).toLocaleString()}
                      </p>
                    </div>
                    <TrackingStatusComponent
                      status={activePackage.status}
                      statusDescription={activePackage.statusDescription}
                      size="lg"
                    />
                  </div>

                  {/* Delivery Estimate */}
                  <div className="flex items-center space-x-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <Clock className="h-5 w-5 text-blue-600" />
                    <div>
                      <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
                        Estimated Delivery
                      </p>
                      <p className="text-sm text-blue-700 dark:text-blue-200">
                        {new Date(activePackage.estimatedDelivery).toLocaleDateString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                        {activePackage.deliveryInfo.estimatedTimeWindow &&
                          `, ${activePackage.deliveryInfo.estimatedTimeWindow}`
                        }
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Tracking Timeline */}
              <TrackingTimeline events={activePackage.events} />

              {/* Tracking Map */}
              <TrackingMap
                origin={activePackage.origin}
                destination={activePackage.destination}
                currentLocation={activePackage.currentLocation}
                isDelivered={activePackage.isDelivered}
              />
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Package Details */}
              <PackageDetailsComponent
                packageDetails={activePackage.packageDetails}
                deliveryInfo={activePackage.deliveryInfo}
                trackingNumber={activePackage.trackingNumber}
                referenceNumber={activePackage.referenceNumber}
                orderId={activePackage.orderId}
              />

              {/* Tracking Notifications */}
              <TrackingNotifications package={activePackage} />

              {/* Customer Support */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Phone className="h-5 w-5 text-green-600" />
                    <span>Need Help?</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Having issues with your package? Our customer support team is here to help.
                  </p>

                  <div className="space-y-2">
                    <Button variant="outline" className="w-full justify-start" size="sm">
                      <Phone className="h-4 w-4 mr-2" />
                      Call Support: +****************
                    </Button>

                    <Button variant="outline" className="w-full justify-start" size="sm">
                      <Mail className="h-4 w-4 mr-2" />
                      Email: <EMAIL>
                    </Button>
                  </div>

                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    <p><strong>Hours:</strong> Mon-Fri 8AM-8PM EST</p>
                    <p><strong>Reference:</strong> {activePackage.trackingNumber}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        ) : (
          /* Empty State */
          <Card>
            <CardContent className="p-12 text-center">
              <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No packages to track
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Enter a tracking number above to get started with package tracking
              </p>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                <p>You can track packages using:</p>
                <ul className="mt-2 space-y-1">
                  <li>• Tracking numbers (LBX123456789)</li>
                  <li>• Reference numbers (REF-2024-001)</li>
                  <li>• Order IDs (ORD-789123)</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Print View (Hidden, only visible when printing) */}
        {activePackage && <TrackingPrintView package={activePackage} />}
      </div>
    </RoleBasedDashboardLayout>
  )
}
