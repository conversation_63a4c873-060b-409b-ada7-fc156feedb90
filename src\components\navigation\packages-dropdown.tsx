'use client'

import { 
  Package, 
  Archive, 
  Combine, 
  Send, 
  Truck, 
  RotateCcw, 
  Trash2,
  ChevronDown
} from 'lucide-react'
import { DropdownMenu, DropdownMenuItem, DropdownMenuSeparator } from '@/components/ui/dropdown-menu'
import { usePackageCounts } from '@/hooks/use-package-counts'
import { cn } from '@/lib/utils'

interface PackagesDropdownProps {
  isActive?: boolean
  className?: string
}

/**
 * Packages Dropdown Navigation Component
 * 
 * Provides a dropdown menu for the "My Packages" navigation item
 * with all package management options and real-time counts
 */
export default function PackagesDropdown({ isActive = false, className }: PackagesDropdownProps) {
  const counts = usePackageCounts()

  const trigger = (
    <div className={cn(
      "group flex items-center px-2 py-2 text-sm font-medium rounded-md cursor-pointer",
      isActive
        ? "bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100"
        : "text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white",
      className
    )}>
      <Package className="mr-3 h-5 w-5" />
      <span className="flex-1">My Packages</span>
      <ChevronDown className="ml-1 h-4 w-4 transition-transform group-hover:rotate-180" />
    </div>
  )

  return (
    <DropdownMenu trigger={trigger}>
      {/* View All Packages */}
      <DropdownMenuItem href="/packages">
        <Package className="mr-3 h-4 w-4" />
        <span>View All Packages</span>
        {counts.total > 0 && (
          <span className="ml-auto text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full">
            {counts.total}
          </span>
        )}
      </DropdownMenuItem>

      <DropdownMenuSeparator />

      {/* Packages in Account */}
      <DropdownMenuItem href="/packages?status=in-account">
        <Archive className="mr-3 h-4 w-4 text-blue-600" />
        <span>Packages in Account</span>
        {counts.inAccount > 0 && (
          <span className="ml-auto text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full font-medium">
            {counts.inAccount}
          </span>
        )}
      </DropdownMenuItem>

      {/* Consolidations */}
      <DropdownMenuItem href="/packages/consolidations">
        <Combine className="mr-3 h-4 w-4 text-purple-600" />
        <span>Consolidations</span>
        {counts.consolidations > 0 && (
          <span className="ml-auto text-xs bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded-full font-medium">
            {counts.consolidations}
          </span>
        )}
      </DropdownMenuItem>

      {/* Ready for Mailout */}
      <DropdownMenuItem href="/packages?status=ready-for-mailout">
        <Send className="mr-3 h-4 w-4 text-green-600" />
        <span>Ready for Mailout</span>
        {counts.readyForMailout > 0 && (
          <span className="ml-auto text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded-full font-medium">
            {counts.readyForMailout}
          </span>
        )}
      </DropdownMenuItem>

      {/* Sent */}
      <DropdownMenuItem href="/packages?status=sent">
        <Truck className="mr-3 h-4 w-4 text-orange-600" />
        <span>Sent</span>
        {counts.sent > 0 && (
          <span className="ml-auto text-xs bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 px-2 py-1 rounded-full font-medium">
            {counts.sent}
          </span>
        )}
      </DropdownMenuItem>

      <DropdownMenuSeparator />

      {/* Return */}
      <DropdownMenuItem href="/packages/returns">
        <RotateCcw className="mr-3 h-4 w-4 text-yellow-600" />
        <span>Return</span>
        {counts.returns > 0 && (
          <span className="ml-auto text-xs bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded-full font-medium">
            {counts.returns}
          </span>
        )}
      </DropdownMenuItem>

      {/* Trashed */}
      <DropdownMenuItem href="/packages/trashed">
        <Trash2 className="mr-3 h-4 w-4 text-red-600" />
        <span>Trashed</span>
        {counts.trashed > 0 && (
          <span className="ml-auto text-xs bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded-full font-medium">
            {counts.trashed}
          </span>
        )}
      </DropdownMenuItem>
    </DropdownMenu>
  )
}
