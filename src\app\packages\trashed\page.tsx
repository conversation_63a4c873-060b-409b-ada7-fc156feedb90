/**
 * Trashed Packages Page - LibyanoEx Forwarding Service
 *
 * Manages deleted/cancelled packages
 * Allows customers to view and restore trashed packages
 */

'use client'

import { useState } from 'react'
import { useAuth } from '@/hooks/use-auth'
import RoleBasedDashboardLayout from '@/components/layout/role-based-dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Trash2, 
  Package, 
  RotateCcw, 
  Calendar, 
  AlertTriangle,
  RefreshCw,
  X
} from 'lucide-react'

// Mock trashed packages data
const mockTrashedPackages = [
  // Empty for now - typically would show cancelled/deleted packages
]

export default function TrashedPackagesPage() {
  const { user } = useAuth()
  const [trashedPackages] = useState(mockTrashedPackages)

  const handleRestore = (packageId: string) => {
    // TODO: Implement restore functionality
    console.log('Restoring package:', packageId)
  }

  const handlePermanentDelete = (packageId: string) => {
    // TODO: Implement permanent delete functionality
    console.log('Permanently deleting package:', packageId)
  }

  return (
    <RoleBasedDashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Trashed Packages
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              View and manage deleted or cancelled packages
            </p>
          </div>
          {trashedPackages.length > 0 && (
            <Button variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Empty Trash
            </Button>
          )}
        </div>

        {/* Stats Card */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Trash2 className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Packages in Trash
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {trashedPackages.length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Trashed Packages List */}
        <Card>
          <CardHeader>
            <CardTitle>Deleted Packages</CardTitle>
            <CardDescription>
              Packages that have been deleted or cancelled. Items are permanently removed after 30 days.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {trashedPackages.length === 0 ? (
              <div className="text-center py-12">
                <Trash2 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Trash is empty
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  No deleted or cancelled packages found. Deleted packages appear here for 30 days before permanent removal.
                </p>
                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg max-w-md mx-auto">
                  <div className="flex items-start space-x-3">
                    <AlertTriangle className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div className="text-sm text-blue-800 dark:text-blue-200">
                      <p className="font-medium mb-1">Good to know:</p>
                      <ul className="space-y-1 text-left">
                        <li>• Deleted packages can be restored within 30 days</li>
                        <li>• Cancelled shipments appear here temporarily</li>
                        <li>• Items are permanently removed after 30 days</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {trashedPackages.map((pkg: any) => (
                  <div
                    key={pkg.id}
                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-red-50 dark:bg-red-900/10"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <Package className="h-5 w-5 text-gray-500" />
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            {pkg.trackingNumber}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {pkg.description}
                          </p>
                        </div>
                        <Badge variant="secondary" className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                          <Trash2 className="h-3 w-3 mr-1" />
                          Deleted
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRestore(pkg.id)}
                        >
                          <RotateCcw className="h-4 w-4 mr-2" />
                          Restore
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handlePermanentDelete(pkg.id)}
                        >
                          <X className="h-4 w-4 mr-2" />
                          Delete Forever
                        </Button>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600 dark:text-gray-400">Weight</p>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {pkg.weight} kg
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-600 dark:text-gray-400">Destination</p>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {pkg.destination}
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-600 dark:text-gray-400">Deleted Date</p>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {new Date(pkg.deletedAt).toLocaleDateString()}
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-600 dark:text-gray-400">Auto-Delete</p>
                        <p className="font-medium text-red-600">
                          {new Date(new Date(pkg.deletedAt).getTime() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Info Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
              <span>Important Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm text-gray-600 dark:text-gray-300">
              <p>• <strong>Restoration Period:</strong> Deleted packages can be restored within 30 days</p>
              <p>• <strong>Automatic Cleanup:</strong> Packages are permanently deleted after 30 days in trash</p>
              <p>• <strong>Data Recovery:</strong> Once permanently deleted, packages cannot be recovered</p>
              <p>• <strong>Billing:</strong> No charges apply for packages in trash status</p>
              <p>• <strong>Notifications:</strong> You'll receive reminders before permanent deletion</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </RoleBasedDashboardLayout>
  )
}
