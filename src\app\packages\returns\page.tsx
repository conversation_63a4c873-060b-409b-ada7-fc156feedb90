/**
 * Package Returns Page - LibyanoEx Forwarding Service
 *
 * Manages return shipments and return requests
 * Allows customers to initiate returns and track return status
 */

'use client'

import { useState } from 'react'
import { useAuth } from '@/hooks/use-auth'
import RoleBasedDashboardLayout from '@/components/layout/role-based-dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  RotateCcw, 
  Package, 
  Plus, 
  Calendar, 
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
  FileText,
  Truck
} from 'lucide-react'

// Mock returns data
const mockReturns = [
  {
    id: 'RET-001',
    originalPackageId: 'PKG-123',
    status: 'pending-approval',
    reason: 'Damaged item',
    requestedAt: '2025-01-12',
    estimatedProcessing: '2025-01-15',
    returnMethod: 'pickup',
    refundAmount: 89.99
  }
]

export default function ReturnsPage() {
  const { user } = useAuth()
  const [returns] = useState(mockReturns)

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending-approval': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'approved': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'in-transit': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'rejected': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending-approval': return <Clock className="h-4 w-4" />
      case 'approved': return <CheckCircle className="h-4 w-4" />
      case 'in-transit': return <Truck className="h-4 w-4" />
      case 'completed': return <CheckCircle className="h-4 w-4" />
      case 'rejected': return <XCircle className="h-4 w-4" />
      default: return <AlertCircle className="h-4 w-4" />
    }
  }

  return (
    <RoleBasedDashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Package Returns
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage return requests and track return shipments
            </p>
          </div>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Request Return
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <RotateCcw className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Returns
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {returns.length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Pending Approval
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {returns.filter(r => r.status === 'pending-approval').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Truck className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    In Transit
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {returns.filter(r => r.status === 'in-transit').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Completed
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {returns.filter(r => r.status === 'completed').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Returns List */}
        <Card>
          <CardHeader>
            <CardTitle>Return Requests</CardTitle>
            <CardDescription>
              Track your return requests and their current status
            </CardDescription>
          </CardHeader>
          <CardContent>
            {returns.length === 0 ? (
              <div className="text-center py-12">
                <RotateCcw className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  No returns yet
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  When you need to return a package, you can request it here
                </p>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Request Return
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {returns.map((returnItem) => (
                  <div
                    key={returnItem.id}
                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                          {returnItem.id}
                        </h3>
                        <Badge className={getStatusColor(returnItem.status)}>
                          {getStatusIcon(returnItem.status)}
                          <span className="ml-1 capitalize">{returnItem.status.replace('-', ' ')}</span>
                        </Badge>
                      </div>
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600 dark:text-gray-400">Original Package</p>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {returnItem.originalPackageId}
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-600 dark:text-gray-400">Reason</p>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {returnItem.reason}
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-600 dark:text-gray-400">Return Method</p>
                        <p className="font-medium text-gray-900 dark:text-white capitalize">
                          {returnItem.returnMethod}
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-600 dark:text-gray-400">Refund Amount</p>
                        <p className="font-medium text-green-600">
                          ${returnItem.refundAmount}
                        </p>
                      </div>
                    </div>

                    <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">
                          Requested: {new Date(returnItem.requestedAt).toLocaleDateString()}
                        </span>
                        <span className="text-gray-600 dark:text-gray-400">
                          Est. Processing: {new Date(returnItem.estimatedProcessing).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Return Policy Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Return Policy</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm text-gray-600 dark:text-gray-300">
              <p>• Returns must be requested within 30 days of delivery</p>
              <p>• Items must be in original condition and packaging</p>
              <p>• Return shipping costs may apply depending on the reason</p>
              <p>• Refunds are processed within 5-7 business days after return approval</p>
              <p>• Some items may not be eligible for return due to customs regulations</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </RoleBasedDashboardLayout>
  )
}
