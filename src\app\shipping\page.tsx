/**
 * Shipping Calculator Page - Rate Calculation System
 * 
 * Comprehensive shipping rate calculator for LibyanoEx forwarding portal
 * 
 * Features:
 * - Multi-carrier rate comparison (DHL, FedEx, UPS, USPS)
 * - Package dimension and weight input
 * - Origin and destination selection
 * - Service level options (Express, Standard, Economy)
 * - Insurance and additional services
 * - Real-time rate calculations
 * - Rate comparison table
 * - Booking integration
 * 
 * Components:
 * - ShippingForm - Package and destination input
 * - RateComparison - Rate comparison table
 * - ServiceOptions - Additional service selection
 * - BookingActions - Rate selection and booking
 */

'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/use-auth'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  Calculator, 
  Package, 
  MapPin, 
  Truck, 
  Clock, 
  Shield, 
  Zap,
  DollarSign,
  Scale,
  Ruler,
  Globe,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react'

// Types for shipping calculation
interface PackageDimensions {
  length: number
  width: number
  height: number
  weight: number
}

interface ShippingAddress {
  country: string
  state: string
  city: string
  zipCode: string
}

interface ShippingRate {
  carrier: string
  service: string
  price: number
  deliveryTime: string
  features: string[]
  logo?: string
}

interface CalculationRequest {
  origin: ShippingAddress
  destination: ShippingAddress
  package: PackageDimensions
  declaredValue: number
  insurance: boolean
  signatureRequired: boolean
}

export default function ShippingCalculatorPage() {
  const { user } = useAuth()
  
  // State management
  const [isCalculating, setIsCalculating] = useState(false)
  const [rates, setRates] = useState<ShippingRate[]>([])
  const [selectedRate, setSelectedRate] = useState<ShippingRate | null>(null)
  const [showResults, setShowResults] = useState(false)
  
  // Form state
  const [formData, setFormData] = useState<CalculationRequest>({
    origin: {
      country: 'United States',
      state: 'NY',
      city: 'New York',
      zipCode: '10001'
    },
    destination: {
      country: '',
      state: '',
      city: '',
      zipCode: ''
    },
    package: {
      length: 0,
      width: 0,
      height: 0,
      weight: 0
    },
    declaredValue: 0,
    insurance: false,
    signatureRequired: false
  })

  // Mock shipping rates - in real app, this would come from carrier APIs
  const mockRates: ShippingRate[] = [
    {
      carrier: 'DHL Express',
      service: 'Express Worldwide',
      price: 89.99,
      deliveryTime: '1-2 business days',
      features: ['Express delivery', 'Tracking included', 'Insurance up to $100']
    },
    {
      carrier: 'FedEx',
      service: 'International Priority',
      price: 76.50,
      deliveryTime: '2-3 business days',
      features: ['Priority handling', 'Tracking included', 'Signature required']
    },
    {
      carrier: 'UPS',
      service: 'Worldwide Express',
      price: 82.25,
      deliveryTime: '1-3 business days',
      features: ['Express service', 'Real-time tracking', 'Insurance included']
    },
    {
      carrier: 'USPS',
      service: 'Priority Mail International',
      price: 45.99,
      deliveryTime: '6-10 business days',
      features: ['Affordable option', 'Basic tracking', 'Customs handling']
    },
    {
      carrier: 'DHL Express',
      service: 'Express Easy',
      price: 65.75,
      deliveryTime: '2-4 business days',
      features: ['Reliable delivery', 'Online tracking', 'Duty handling']
    },
    {
      carrier: 'FedEx',
      service: 'International Economy',
      price: 52.30,
      deliveryTime: '4-6 business days',
      features: ['Economy option', 'Tracking included', 'Customs clearance']
    }
  ]

  /**
   * Handles form input changes
   */
  const handleInputChange = (section: keyof CalculationRequest, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }))
  }

  /**
   * Validates form data
   */
  const validateForm = (): boolean => {
    const { destination, package: pkg, declaredValue } = formData
    
    return !!(
      destination.country &&
      destination.city &&
      destination.zipCode &&
      pkg.length > 0 &&
      pkg.width > 0 &&
      pkg.height > 0 &&
      pkg.weight > 0 &&
      declaredValue > 0
    )
  }

  /**
   * Calculates shipping rates
   */
  const calculateRates = async () => {
    if (!validateForm()) {
      alert('Please fill in all required fields')
      return
    }

    setIsCalculating(true)
    setShowResults(false)
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Apply pricing modifiers based on package details
      const modifiedRates = mockRates.map(rate => {
        let adjustedPrice = rate.price
        
        // Weight modifier (using chargeable weight)
        const chargeableWeight = Math.max(formData.package.weight, (formData.package.length * formData.package.width * formData.package.height) / 5000)
        if (chargeableWeight > 5) {
          adjustedPrice *= 1.2
        }
        
        // Insurance modifier
        if (formData.insurance) {
          adjustedPrice += formData.declaredValue * 0.01
        }

        // Signature required modifier
        if (formData.signatureRequired) {
          adjustedPrice += 5
        }
        
        return {
          ...rate,
          price: Math.round(adjustedPrice * 100) / 100
        }
      })
      
      // Sort by price
      modifiedRates.sort((a, b) => a.price - b.price)
      
      setRates(modifiedRates)
      setShowResults(true)
    } catch (error) {
      console.error('Error calculating rates:', error)
      alert('Error calculating shipping rates. Please try again.')
    } finally {
      setIsCalculating(false)
    }
  }

  /**
   * Handles rate selection
   */
  const handleSelectRate = (rate: ShippingRate) => {
    setSelectedRate(rate)
  }

  /**
   * Handles booking
   */
  const handleBookShipment = () => {
    if (!selectedRate) return
    
    // TODO: Implement booking functionality
    console.log('Booking shipment with rate:', selectedRate)
    alert(`Booking shipment with ${selectedRate.carrier} - ${selectedRate.service}`)
  }

  /**
   * Calculates volumetric weight using standard formula (L x W x H / 5000)
   */
  const calculateVolumetricWeight = (): number => {
    const { length, width, height } = formData.package
    if (length <= 0 || width <= 0 || height <= 0) return 0
    // Standard volumetric weight formula: (L x W x H in cm) / 5000
    return (length * width * height) / 5000
  }

  /**
   * Calculates chargeable weight (higher of actual weight vs volumetric weight)
   */
  const calculateChargeableWeight = (): number => {
    const actualWeight = formData.package.weight
    const volumetricWeight = calculateVolumetricWeight()
    return Math.max(actualWeight, volumetricWeight)
  }

  /**
   * Gets carrier icon color
   */
  const getCarrierColor = (carrier: string): string => {
    const colors: { [key: string]: string } = {
      'DHL Express': 'text-yellow-600',
      'FedEx': 'text-purple-600',
      'UPS': 'text-amber-600',
      'USPS': 'text-blue-600'
    }
    return colors[carrier] || 'text-gray-600'
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Shipping Calculator
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Compare shipping rates from multiple carriers and find the best option
            </p>
          </div>
          <div className="mt-4 md:mt-0">
            <Badge variant="secondary" className="text-sm">
              <Globe className="h-4 w-4 mr-1" />
              International Shipping Available
            </Badge>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Shipping Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Package Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Package className="h-5 w-5 text-blue-600" />
                  <span>Package Information</span>
                </CardTitle>
                <CardDescription>
                  Enter your package dimensions and weight
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="length">Length (cm)</Label>
                    <Input
                      id="length"
                      type="number"
                      placeholder="0"
                      value={formData.package.length || ''}
                      onChange={(e) => handleInputChange('package', 'length', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="width">Width (cm)</Label>
                    <Input
                      id="width"
                      type="number"
                      placeholder="0"
                      value={formData.package.width || ''}
                      onChange={(e) => handleInputChange('package', 'width', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="height">Height (cm)</Label>
                    <Input
                      id="height"
                      type="number"
                      placeholder="0"
                      value={formData.package.height || ''}
                      onChange={(e) => handleInputChange('package', 'height', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="weight">Weight (kg)</Label>
                    <Input
                      id="weight"
                      type="number"
                      placeholder="0"
                      value={formData.package.weight || ''}
                      onChange={(e) => handleInputChange('package', 'weight', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                </div>

                {calculateVolumetricWeight() > 0 && (
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg space-y-2">
                    <div className="flex items-center space-x-2 text-blue-800 dark:text-blue-200">
                      <Scale className="h-4 w-4" />
                      <span className="text-sm">
                        Package Volumetric Weight: {calculateVolumetricWeight().toFixed(2)} kg
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 text-blue-800 dark:text-blue-200">
                      <Ruler className="h-4 w-4" />
                      <span className="text-sm font-medium">
                        Chargeable Weight: {calculateChargeableWeight().toFixed(2)} kg
                      </span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Destination Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <MapPin className="h-5 w-5 text-green-600" />
                  <span>Destination</span>
                </CardTitle>
                <CardDescription>
                  Where are you shipping to?
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="destCountry">Country</Label>
                    <Input
                      id="destCountry"
                      placeholder="United States"
                      value={formData.destination.country}
                      onChange={(e) => handleInputChange('destination', 'country', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="destState">State/Province</Label>
                    <Input
                      id="destState"
                      placeholder="CA"
                      value={formData.destination.state}
                      onChange={(e) => handleInputChange('destination', 'state', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="destCity">City</Label>
                    <Input
                      id="destCity"
                      placeholder="Los Angeles"
                      value={formData.destination.city}
                      onChange={(e) => handleInputChange('destination', 'city', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="destZip">ZIP/Postal Code</Label>
                    <Input
                      id="destZip"
                      placeholder="90210"
                      value={formData.destination.zipCode}
                      onChange={(e) => handleInputChange('destination', 'zipCode', e.target.value)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Package Value and Options */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <DollarSign className="h-5 w-5 text-purple-600" />
                  <span>Value & Options</span>
                </CardTitle>
                <CardDescription>
                  Package value and additional services
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="declaredValue">Declared Value ($)</Label>
                  <Input
                    id="declaredValue"
                    type="number"
                    step="0.01"
                    placeholder="100.00"
                    value={formData.declaredValue || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, declaredValue: parseFloat(e.target.value) || 0 }))}
                  />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="insurance"
                      checked={formData.insurance}
                      onChange={(e) => setFormData(prev => ({ ...prev, insurance: e.target.checked }))}
                    />
                    <Label htmlFor="insurance" className="flex items-center space-x-2">
                      <Shield className="h-4 w-4 text-blue-600" />
                      <span>Add Insurance Coverage</span>
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="signature"
                      checked={formData.signatureRequired}
                      onChange={(e) => setFormData(prev => ({ ...prev, signatureRequired: e.target.checked }))}
                    />
                    <Label htmlFor="signature" className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span>Signature Required</span>
                    </Label>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Calculate Button */}
            <Card>
              <CardContent className="pt-6">
                <Button
                  onClick={calculateRates}
                  disabled={isCalculating || !validateForm()}
                  className="w-full h-12 text-lg"
                >
                  {isCalculating ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      Calculating Rates...
                    </>
                  ) : (
                    <>
                      <Calculator className="mr-2 h-5 w-5" />
                      Calculate Shipping Rates
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Results Panel */}
          <div className="space-y-6">
            {/* Origin Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <MapPin className="h-5 w-5 text-blue-600" />
                  <span>Shipping From</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="font-medium text-gray-900 dark:text-white">LibyanoEx Warehouse</p>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    {formData.origin.city}, {formData.origin.state} {formData.origin.zipCode}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    {formData.origin.country}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Rate Results */}
            {showResults && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Truck className="h-5 w-5 text-purple-600" />
                    <span>Shipping Options</span>
                  </CardTitle>
                  <CardDescription>
                    Compare rates from different carriers
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {rates.map((rate, index) => (
                      <div
                        key={index}
                        className={`p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
                          selectedRate === rate
                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                            : 'border-gray-200 dark:border-gray-700'
                        }`}
                        onClick={() => handleSelectRate(rate)}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <Truck className={`h-4 w-4 ${getCarrierColor(rate.carrier)}`} />
                            <span className="font-medium text-gray-900 dark:text-white">
                              {rate.carrier}
                            </span>
                          </div>
                          <span className="text-lg font-bold text-gray-900 dark:text-white">
                            ${rate.price}
                          </span>
                        </div>

                        <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                          {rate.service}
                        </p>

                        <div className="flex items-center space-x-2 mb-2">
                          <Clock className="h-3 w-3 text-gray-500" />
                          <span className="text-xs text-gray-500">
                            {rate.deliveryTime}
                          </span>
                        </div>

                        <div className="flex flex-wrap gap-1">
                          {rate.features.map((feature, featureIndex) => (
                            <Badge key={featureIndex} variant="secondary" className="text-xs">
                              {feature}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>

                  {selectedRate && (
                    <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                      <Button onClick={handleBookShipment} className="w-full">
                        <CheckCircle className="mr-2 h-4 w-4" />
                        Book with {selectedRate.carrier}
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Shipping Tips */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Zap className="h-5 w-5 text-yellow-600" />
                  <span>Shipping Tips</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Accurate dimensions help ensure correct pricing
                    </p>
                  </div>
                  <div className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Consider package consolidation for multiple items
                    </p>
                  </div>
                  <div className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Insurance is recommended for valuable items
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
