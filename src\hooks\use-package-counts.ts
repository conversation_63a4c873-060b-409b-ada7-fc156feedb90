'use client'

import { useState, useEffect } from 'react'

interface PackageCounts {
  total: number
  inAccount: number
  consolidations: number
  readyForMailout: number
  sent: number
  returns: number
  trashed: number
}

/**
 * Hook to get package counts for different statuses
 * 
 * This would typically fetch from an API, but for now uses mock data
 * that simulates real-time package counts
 */
export function usePackageCounts(): PackageCounts {
  const [counts, setCounts] = useState<PackageCounts>({
    total: 0,
    inAccount: 0,
    consolidations: 0,
    readyForMailout: 0,
    sent: 0,
    returns: 0,
    trashed: 0
  })

  useEffect(() => {
    // Mock data - in real app, this would be an API call
    const mockCounts: PackageCounts = {
      total: 23,
      inAccount: 5,
      consolidations: 2,
      readyForMailout: 3,
      sent: 12,
      returns: 1,
      trashed: 0
    }

    // Simulate API delay
    const timer = setTimeout(() => {
      setCounts(mockCounts)
    }, 100)

    return () => clearTimeout(timer)
  }, [])

  return counts
}

/**
 * Hook to get packages by status
 * 
 * This would filter packages based on their status
 * For now, returns mock data
 */
export function usePackagesByStatus(status: string) {
  const [packages, setPackages] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Mock implementation - would fetch filtered packages from API
    setIsLoading(true)
    
    const timer = setTimeout(() => {
      // Mock filtered packages based on status
      const mockPackages = []
      setPackages(mockPackages)
      setIsLoading(false)
    }, 500)

    return () => clearTimeout(timer)
  }, [status])

  return { packages, isLoading }
}
