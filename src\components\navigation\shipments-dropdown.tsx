'use client'

import { 
  Send, 
  FileText, 
  Plus, 
  Clock, 
  CheckCircle, 
  Truck, 
  Package2, 
  XCircle,
  ChevronDown
} from 'lucide-react'
import { DropdownMenu, DropdownMenuItem, DropdownMenuSeparator } from '@/components/ui/dropdown-menu'
import { useShipmentCounts } from '@/hooks/use-shipment-counts'
import { cn } from '@/lib/utils'

interface ShipmentsDropdownProps {
  isActive?: boolean
  className?: string
  isOpen?: boolean
  onToggle?: () => void
}

/**
 * Shipments Dropdown Navigation Component
 *
 * Provides a dropdown menu for the "Request Shipment" navigation item
 * with all Door-to-Door shipment management options and real-time counts
 */
export default function ShipmentsDropdown({ isActive = false, className, isOpen = false, onToggle }: ShipmentsDropdownProps) {
  const counts = useShipmentCounts()

  const trigger = (
    <div
      className={cn(
        "flex items-center px-2 py-2 text-sm font-medium rounded-md cursor-pointer",
        isActive
          ? "bg-blue-100 text-blue-900 dark:bg-blue-900 dark:text-blue-100"
          : "text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-white",
        className
      )}
      onClick={onToggle}
    >
      <Send className="mr-3 h-5 w-5" />
      <span className="flex-1">Request Shipment</span>
      <ChevronDown className={cn(
        "ml-1 h-4 w-4 transition-transform",
        isOpen ? "rotate-180" : ""
      )} />
    </div>
  )

  return (
    <DropdownMenu trigger={trigger} isOpen={isOpen} onToggle={onToggle}>
      {/* View All Shipments */}
      <DropdownMenuItem href="/shipments">
        <FileText className="mr-3 h-4 w-4" />
        <span>View All Shipments</span>
        {counts.total > 0 && (
          <span className="ml-auto text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full">
            {counts.total}
          </span>
        )}
      </DropdownMenuItem>

      <DropdownMenuSeparator />

      {/* Request Shipment */}
      <DropdownMenuItem href="/shipments/request">
        <Plus className="mr-3 h-4 w-4 text-green-600" />
        <span>Request Shipment</span>
      </DropdownMenuItem>

      <DropdownMenuSeparator />

      {/* Under Review */}
      <DropdownMenuItem href="/shipments?status=under-review">
        <Clock className="mr-3 h-4 w-4 text-yellow-600" />
        <span>Under Review</span>
        {counts.underReview > 0 && (
          <span className="ml-auto text-xs bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded-full font-medium">
            {counts.underReview}
          </span>
        )}
      </DropdownMenuItem>

      {/* Approved */}
      <DropdownMenuItem href="/shipments?status=approved">
        <CheckCircle className="mr-3 h-4 w-4 text-blue-600" />
        <span>Approved</span>
        {counts.approved > 0 && (
          <span className="ml-auto text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full font-medium">
            {counts.approved}
          </span>
        )}
      </DropdownMenuItem>

      {/* Ready to Send */}
      <DropdownMenuItem href="/shipments?status=ready-to-send">
        <Truck className="mr-3 h-4 w-4 text-purple-600" />
        <span>Ready to Send</span>
        {counts.readyToSend > 0 && (
          <span className="ml-auto text-xs bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded-full font-medium">
            {counts.readyToSend}
          </span>
        )}
      </DropdownMenuItem>

      {/* Completed */}
      <DropdownMenuItem href="/shipments?status=completed">
        <Package2 className="mr-3 h-4 w-4 text-green-600" />
        <span>Completed</span>
        {counts.completed > 0 && (
          <span className="ml-auto text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded-full font-medium">
            {counts.completed}
          </span>
        )}
      </DropdownMenuItem>

      <DropdownMenuSeparator />

      {/* Cancelled */}
      <DropdownMenuItem href="/shipments?status=cancelled">
        <XCircle className="mr-3 h-4 w-4 text-red-600" />
        <span>Cancelled</span>
        {counts.cancelled > 0 && (
          <span className="ml-auto text-xs bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded-full font-medium">
            {counts.cancelled}
          </span>
        )}
      </DropdownMenuItem>
    </DropdownMenu>
  )
}
