/**
 * Shipment Request Form - LibyanoEx Door-to-Door Service
 *
 * Multi-step form for requesting Door-to-Door shipments
 * From US, Canada, UK to Libya via DHL integration
 */

'use client'

import { useState } from 'react'
import { useAuth } from '@/hooks/use-auth'
import RoleBasedDashboardLayout from '@/components/layout/role-based-dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { 
  Send, 
  User, 
  MapPin, 
  Package, 
  FileText, 
  CreditCard,
  CheckCircle,
  ArrowLeft,
  ArrowRight,
  Plus,
  Trash2,
  Calculator,
  Scale
} from 'lucide-react'

// Form step type
type FormStep = 'sender-recipient' | 'packages' | 'packing-list' | 'agreements' | 'review'

// Form data interfaces
interface SenderInfo {
  name: string
  address: string
  city: string
  state: string
  postalCode: string
  country: string
  phone: string
  email: string
}

interface RecipientInfo {
  name: string
  address: string
  city: string
  phone: string
  email: string
}

interface PackageInfo {
  id: string
  length: number
  width: number
  height: number
  weight: number
}

interface PackingListItem {
  id: string
  quantity: number
  description: string
  countryOfOrigin: string
  unitPrice: number
}

interface FormData {
  sender: SenderInfo
  recipient: RecipientInfo
  packages: PackageInfo[]
  packingList: PackingListItem[]
  agreements: {
    termsAccepted: boolean
    declarationAccepted: boolean
  }
}

export default function ShipmentRequestPage() {
  const { user } = useAuth()
  const [currentStep, setCurrentStep] = useState<FormStep>('sender-recipient')
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Initialize form data with user info
  const [formData, setFormData] = useState<FormData>({
    sender: {
      name: user?.name || '',
      address: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'US',
      phone: user?.phone || '',
      email: user?.email || ''
    },
    recipient: {
      name: '',
      address: '',
      city: '',
      phone: '',
      email: ''
    },
    packages: [
      {
        id: '1',
        length: 0,
        width: 0,
        height: 0,
        weight: 0
      }
    ],
    packingList: [
      {
        id: '1',
        quantity: 1,
        description: '',
        countryOfOrigin: 'US',
        unitPrice: 0
      }
    ],
    agreements: {
      termsAccepted: false,
      declarationAccepted: false
    }
  })

  // Calculate package metrics
  const calculateVolumetricWeight = (pkg: PackageInfo): number => {
    if (pkg.length <= 0 || pkg.width <= 0 || pkg.height <= 0) return 0
    // DHL volumetric weight formula: (L x W x H in cm) / 5000
    return (pkg.length * pkg.width * pkg.height) / 5000
  }

  const getTotalMetrics = () => {
    const totalPackages = formData.packages.length
    const totalActualWeight = formData.packages.reduce((sum, pkg) => sum + pkg.weight, 0)
    const totalVolumetricWeight = formData.packages.reduce((sum, pkg) => sum + calculateVolumetricWeight(pkg), 0)
    const chargeableWeight = Math.max(totalActualWeight, totalVolumetricWeight)
    const totalDeclaredValue = formData.packingList.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0)

    return {
      totalPackages,
      totalActualWeight,
      totalVolumetricWeight,
      chargeableWeight,
      totalDeclaredValue
    }
  }

  const addPackage = () => {
    const newPackage: PackageInfo = {
      id: Date.now().toString(),
      length: 0,
      width: 0,
      height: 0,
      weight: 0
    }
    setFormData(prev => ({
      ...prev,
      packages: [...prev.packages, newPackage]
    }))
  }

  const removePackage = (id: string) => {
    if (formData.packages.length > 1) {
      setFormData(prev => ({
        ...prev,
        packages: prev.packages.filter(pkg => pkg.id !== id)
      }))
    }
  }

  const updatePackage = (id: string, field: keyof PackageInfo, value: number) => {
    setFormData(prev => ({
      ...prev,
      packages: prev.packages.map(pkg => 
        pkg.id === id ? { ...pkg, [field]: value } : pkg
      )
    }))
  }

  const addPackingListItem = () => {
    const newItem: PackingListItem = {
      id: Date.now().toString(),
      quantity: 1,
      description: '',
      countryOfOrigin: 'US',
      unitPrice: 0
    }
    setFormData(prev => ({
      ...prev,
      packingList: [...prev.packingList, newItem]
    }))
  }

  const removePackingListItem = (id: string) => {
    if (formData.packingList.length > 1) {
      setFormData(prev => ({
        ...prev,
        packingList: prev.packingList.filter(item => item.id !== id)
      }))
    }
  }

  const updatePackingListItem = (id: string, field: keyof PackingListItem, value: any) => {
    setFormData(prev => ({
      ...prev,
      packingList: prev.packingList.map(item => 
        item.id === id ? { ...item, [field]: value } : item
      )
    }))
  }

  const steps = [
    { id: 'sender-recipient', title: 'Sender & Recipient', icon: User },
    { id: 'packages', title: 'Package Information', icon: Package },
    { id: 'packing-list', title: 'Packing List', icon: FileText },
    { id: 'agreements', title: 'Legal Agreements', icon: CheckCircle },
    { id: 'review', title: 'Review & Submit', icon: Send }
  ]

  const currentStepIndex = steps.findIndex(step => step.id === currentStep)
  const isLastStep = currentStepIndex === steps.length - 1
  const isFirstStep = currentStepIndex === 0

  const canProceedToNext = () => {
    switch (currentStep) {
      case 'sender-recipient':
        return formData.sender.name && formData.sender.address && formData.recipient.name && formData.recipient.address
      case 'packages':
        return formData.packages.every(pkg => pkg.length > 0 && pkg.width > 0 && pkg.height > 0 && pkg.weight > 0)
      case 'packing-list':
        return formData.packingList.every(item => item.description && item.unitPrice > 0)
      case 'agreements':
        return formData.agreements.termsAccepted && formData.agreements.declarationAccepted
      default:
        return true
    }
  }

  const handleNext = () => {
    if (canProceedToNext() && !isLastStep) {
      setCurrentStep(steps[currentStepIndex + 1].id as FormStep)
    }
  }

  const handlePrevious = () => {
    if (!isFirstStep) {
      setCurrentStep(steps[currentStepIndex - 1].id as FormStep)
    }
  }

  const handleSubmit = async () => {
    if (!canProceedToNext()) return

    setIsSubmitting(true)
    try {
      // TODO: Submit form data to API
      console.log('Submitting shipment request:', formData)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Redirect to shipments page or show success message
      alert('Shipment request submitted successfully!')
    } catch (error) {
      console.error('Error submitting shipment request:', error)
      alert('Error submitting request. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const metrics = getTotalMetrics()

  return (
    <RoleBasedDashboardLayout>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Request Door-to-Door Shipment
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Send packages from US, Canada, UK directly to Libya via DHL
          </p>
        </div>

        {/* Progress Steps */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => {
                const Icon = step.icon
                const isActive = step.id === currentStep
                const isCompleted = index < currentStepIndex
                
                return (
                  <div key={step.id} className="flex items-center">
                    <div className={`
                      flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors
                      ${isActive 
                        ? 'border-blue-600 bg-blue-600 text-white' 
                        : isCompleted 
                          ? 'border-green-600 bg-green-600 text-white'
                          : 'border-gray-300 bg-white text-gray-400'
                      }
                    `}>
                      <Icon className="h-5 w-5" />
                    </div>
                    <div className="ml-3 hidden md:block">
                      <p className={`text-sm font-medium ${isActive ? 'text-blue-600' : 'text-gray-500'}`}>
                        {step.title}
                      </p>
                    </div>
                    {index < steps.length - 1 && (
                      <div className={`
                        w-12 h-0.5 mx-4 transition-colors
                        ${isCompleted ? 'bg-green-600' : 'bg-gray-300'}
                      `} />
                    )}
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Package Metrics Summary */}
        {(currentStep === 'packages' || currentStep === 'packing-list' || currentStep === 'review') && (
          <Card>
            <CardContent className="p-4">
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                <div className="text-center">
                  <p className="text-gray-600 dark:text-gray-400">Packages</p>
                  <p className="font-bold text-lg">{metrics.totalPackages}</p>
                </div>
                <div className="text-center">
                  <p className="text-gray-600 dark:text-gray-400">Actual Weight</p>
                  <p className="font-bold text-lg">{metrics.totalActualWeight.toFixed(2)} kg</p>
                </div>
                <div className="text-center">
                  <p className="text-gray-600 dark:text-gray-400">Volumetric Weight</p>
                  <p className="font-bold text-lg">{metrics.totalVolumetricWeight.toFixed(2)} kg</p>
                </div>
                <div className="text-center">
                  <p className="text-gray-600 dark:text-gray-400">Chargeable Weight</p>
                  <p className="font-bold text-lg text-blue-600">{metrics.chargeableWeight.toFixed(2)} kg</p>
                </div>
                <div className="text-center">
                  <p className="text-gray-600 dark:text-gray-400">Declared Value</p>
                  <p className="font-bold text-lg text-green-600">${metrics.totalDeclaredValue.toFixed(2)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Form Content */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              {(() => {
                const StepIcon = steps.find(s => s.id === currentStep)?.icon || Send
                return <StepIcon className="h-5 w-5" />
              })()}
              <span>{steps.find(s => s.id === currentStep)?.title}</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            {/* Step content will be rendered here */}
            {currentStep === 'sender-recipient' && (
              <div className="space-y-6">
                {/* Sender Information */}
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <User className="h-5 w-5 mr-2" />
                    Sender Information
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="senderName">Full Name *</Label>
                      <Input
                        id="senderName"
                        value={formData.sender.name}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          sender: { ...prev.sender, name: e.target.value }
                        }))}
                        placeholder="John Smith"
                      />
                    </div>
                    <div>
                      <Label htmlFor="senderEmail">Email *</Label>
                      <Input
                        id="senderEmail"
                        type="email"
                        value={formData.sender.email}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          sender: { ...prev.sender, email: e.target.value }
                        }))}
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div className="md:col-span-2">
                      <Label htmlFor="senderAddress">Address *</Label>
                      <Input
                        id="senderAddress"
                        value={formData.sender.address}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          sender: { ...prev.sender, address: e.target.value }
                        }))}
                        placeholder="123 Main Street"
                      />
                    </div>
                    <div>
                      <Label htmlFor="senderCity">City *</Label>
                      <Input
                        id="senderCity"
                        value={formData.sender.city}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          sender: { ...prev.sender, city: e.target.value }
                        }))}
                        placeholder="New York"
                      />
                    </div>
                    <div>
                      <Label htmlFor="senderPhone">Phone *</Label>
                      <Input
                        id="senderPhone"
                        value={formData.sender.phone}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          sender: { ...prev.sender, phone: e.target.value }
                        }))}
                        placeholder="+****************"
                      />
                    </div>
                  </div>
                </div>

                {/* Recipient Information */}
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <MapPin className="h-5 w-5 mr-2" />
                    Recipient Information (Libya)
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="recipientName">Full Name *</Label>
                      <Input
                        id="recipientName"
                        value={formData.recipient.name}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          recipient: { ...prev.recipient, name: e.target.value }
                        }))}
                        placeholder="Ahmed Al-Mansouri"
                      />
                    </div>
                    <div>
                      <Label htmlFor="recipientEmail">Email</Label>
                      <Input
                        id="recipientEmail"
                        type="email"
                        value={formData.recipient.email}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          recipient: { ...prev.recipient, email: e.target.value }
                        }))}
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div className="md:col-span-2">
                      <Label htmlFor="recipientAddress">Address in Libya *</Label>
                      <Textarea
                        id="recipientAddress"
                        value={formData.recipient.address}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          recipient: { ...prev.recipient, address: e.target.value }
                        }))}
                        placeholder="Street address, area, city"
                        rows={3}
                      />
                    </div>
                    <div>
                      <Label htmlFor="recipientCity">City *</Label>
                      <Input
                        id="recipientCity"
                        value={formData.recipient.city}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          recipient: { ...prev.recipient, city: e.target.value }
                        }))}
                        placeholder="Tripoli"
                      />
                    </div>
                    <div>
                      <Label htmlFor="recipientPhone">Phone *</Label>
                      <Input
                        id="recipientPhone"
                        value={formData.recipient.phone}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          recipient: { ...prev.recipient, phone: e.target.value }
                        }))}
                        placeholder="+218 XX XXX XXXX"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {currentStep === 'packages' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold flex items-center">
                    <Package className="h-5 w-5 mr-2" />
                    Package Information
                  </h3>
                  <Button onClick={addPackage} variant="outline" size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Package
                  </Button>
                </div>

                <div className="space-y-4">
                  {formData.packages.map((pkg, index) => (
                    <div key={pkg.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-medium">Package {index + 1}</h4>
                        {formData.packages.length > 1 && (
                          <Button
                            onClick={() => removePackage(pkg.id)}
                            variant="outline"
                            size="sm"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div>
                          <Label htmlFor={`length-${pkg.id}`}>Length (cm) *</Label>
                          <Input
                            id={`length-${pkg.id}`}
                            type="number"
                            value={pkg.length || ''}
                            onChange={(e) => updatePackage(pkg.id, 'length', parseFloat(e.target.value) || 0)}
                            placeholder="30"
                          />
                        </div>
                        <div>
                          <Label htmlFor={`width-${pkg.id}`}>Width (cm) *</Label>
                          <Input
                            id={`width-${pkg.id}`}
                            type="number"
                            value={pkg.width || ''}
                            onChange={(e) => updatePackage(pkg.id, 'width', parseFloat(e.target.value) || 0)}
                            placeholder="20"
                          />
                        </div>
                        <div>
                          <Label htmlFor={`height-${pkg.id}`}>Height (cm) *</Label>
                          <Input
                            id={`height-${pkg.id}`}
                            type="number"
                            value={pkg.height || ''}
                            onChange={(e) => updatePackage(pkg.id, 'height', parseFloat(e.target.value) || 0)}
                            placeholder="15"
                          />
                        </div>
                        <div>
                          <Label htmlFor={`weight-${pkg.id}`}>Weight (kg) *</Label>
                          <Input
                            id={`weight-${pkg.id}`}
                            type="number"
                            step="0.1"
                            value={pkg.weight || ''}
                            onChange={(e) => updatePackage(pkg.id, 'weight', parseFloat(e.target.value) || 0)}
                            placeholder="2.5"
                          />
                        </div>
                      </div>

                      {/* Package calculations */}
                      {pkg.length > 0 && pkg.width > 0 && pkg.height > 0 && (
                        <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <p className="text-gray-600 dark:text-gray-400">Volumetric Weight</p>
                              <p className="font-medium">{calculateVolumetricWeight(pkg).toFixed(2)} kg</p>
                            </div>
                            <div>
                              <p className="text-gray-600 dark:text-gray-400">Chargeable Weight</p>
                              <p className="font-medium text-blue-600">
                                {Math.max(pkg.weight, calculateVolumetricWeight(pkg)).toFixed(2)} kg
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {currentStep === 'packing-list' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    Packing List
                  </h3>
                  <Button onClick={addPackingListItem} variant="outline" size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Item
                  </Button>
                </div>

                <div className="space-y-4">
                  {formData.packingList.map((item, index) => (
                    <div key={item.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-medium">Item {index + 1}</h4>
                        {formData.packingList.length > 1 && (
                          <Button
                            onClick={() => removePackingListItem(item.id)}
                            variant="outline"
                            size="sm"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                          <Label htmlFor={`quantity-${item.id}`}>Quantity *</Label>
                          <Input
                            id={`quantity-${item.id}`}
                            type="number"
                            min="1"
                            value={item.quantity || ''}
                            onChange={(e) => updatePackingListItem(item.id, 'quantity', parseInt(e.target.value) || 1)}
                            placeholder="1"
                          />
                        </div>
                        <div className="md:col-span-2">
                          <Label htmlFor={`description-${item.id}`}>Description of Goods *</Label>
                          <Input
                            id={`description-${item.id}`}
                            value={item.description}
                            onChange={(e) => updatePackingListItem(item.id, 'description', e.target.value)}
                            placeholder="Electronics - Smartphone"
                          />
                        </div>
                        <div>
                          <Label htmlFor={`unitPrice-${item.id}`}>Unit Price (USD) *</Label>
                          <Input
                            id={`unitPrice-${item.id}`}
                            type="number"
                            step="0.01"
                            value={item.unitPrice || ''}
                            onChange={(e) => updatePackingListItem(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                            placeholder="299.99"
                          />
                        </div>
                      </div>

                      <div className="mt-4">
                        <Label htmlFor={`country-${item.id}`}>Country of Origin</Label>
                        <Input
                          id={`country-${item.id}`}
                          value={item.countryOfOrigin}
                          onChange={(e) => updatePackingListItem(item.id, 'countryOfOrigin', e.target.value)}
                          placeholder="United States"
                        />
                      </div>

                      {/* Item total */}
                      <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600 dark:text-gray-400">Item Total:</span>
                          <span className="font-medium text-green-600">
                            ${(item.quantity * item.unitPrice).toFixed(2)}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {currentStep === 'agreements' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold flex items-center">
                  <CheckCircle className="h-5 w-5 mr-2" />
                  Legal Agreements
                </h3>

                <div className="space-y-4">
                  <div className="flex items-start space-x-3 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <Checkbox
                      id="terms"
                      checked={formData.agreements.termsAccepted}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        agreements: { ...prev.agreements, termsAccepted: e.target.checked }
                      }))}
                    />
                    <div className="flex-1">
                      <Label htmlFor="terms" className="text-sm font-medium">
                        Terms of Use and Privacy Policy *
                      </Label>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        I agree to the LibyanoEx Terms of Use and Privacy Policy. I understand the shipping terms,
                        conditions, and liability limitations for Door-to-Door service.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <Checkbox
                      id="declaration"
                      checked={formData.agreements.declarationAccepted}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        agreements: { ...prev.agreements, declarationAccepted: e.target.checked }
                      }))}
                    />
                    <div className="flex-1">
                      <Label htmlFor="declaration" className="text-sm font-medium">
                        Declaration of Accuracy *
                      </Label>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        I declare that all information in this shipment request and packing list is true and correct.
                        I understand that providing false information may result in customs delays, penalties, or shipment rejection.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                  <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">Important Notes:</h4>
                  <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                    <li>• Prohibited items cannot be shipped and may result in shipment rejection</li>
                    <li>• Customs duties and taxes in Libya are the recipient's responsibility</li>
                    <li>• DHL may require additional documentation for certain items</li>
                    <li>• Delivery times are estimates and may vary due to customs processing</li>
                  </ul>
                </div>
              </div>
            )}

            {currentStep === 'review' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold flex items-center">
                  <Send className="h-5 w-5 mr-2" />
                  Review & Submit
                </h3>

                {/* Summary sections */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Sender</h4>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        <p>{formData.sender.name}</p>
                        <p>{formData.sender.address}</p>
                        <p>{formData.sender.city}, {formData.sender.state}</p>
                        <p>{formData.sender.email}</p>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Recipient</h4>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        <p>{formData.recipient.name}</p>
                        <p>{formData.recipient.address}</p>
                        <p>{formData.recipient.city}, Libya</p>
                        <p>{formData.recipient.email}</p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Package Summary</h4>
                      <div className="text-sm space-y-1">
                        <p>Total Packages: {metrics.totalPackages}</p>
                        <p>Total Weight: {metrics.totalActualWeight.toFixed(2)} kg</p>
                        <p>Volumetric Weight: {metrics.totalVolumetricWeight.toFixed(2)} kg</p>
                        <p className="font-medium">Chargeable Weight: {metrics.chargeableWeight.toFixed(2)} kg</p>
                        <p className="font-medium text-green-600">Total Value: ${metrics.totalDeclaredValue.toFixed(2)}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                  <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Next Steps:</h4>
                  <ol className="text-sm text-blue-700 dark:text-blue-300 space-y-1 list-decimal list-inside">
                    <li>Your shipment request will be reviewed by our team (1-2 business days)</li>
                    <li>Once approved, you'll receive payment instructions</li>
                    <li>After payment, DHL will schedule pickup from your address</li>
                    <li>You'll receive tracking information once DHL collects the package</li>
                  </ol>
                </div>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-8">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={isFirstStep}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>
              
              {isLastStep ? (
                <Button
                  onClick={handleSubmit}
                  disabled={!canProceedToNext() || isSubmitting}
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Request'}
                  <Send className="h-4 w-4 ml-2" />
                </Button>
              ) : (
                <Button
                  onClick={handleNext}
                  disabled={!canProceedToNext()}
                >
                  Next
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </RoleBasedDashboardLayout>
  )
}
