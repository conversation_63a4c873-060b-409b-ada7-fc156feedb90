/**
 * Packages Page - Package Management System
 * 
 * Comprehensive package management interface for LibyanoEx shipping portal
 * 
 * Features:
 * - Package listing with filtering and sorting
 * - Package creation and editing
 * - Status tracking and updates
 * - Bulk operations
 * - Search and filter functionality
 * - Export capabilities
 * - Responsive design optimized for package management
 * 
 * Components:
 * - PackageList - Main package listing
 * - PackageFilters - Search and filter controls
 * - PackageActions - Bulk action buttons
 * - CreatePackageDialog - New package creation
 */

'use client'

import { useState, useMemo, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { useAuth } from '@/hooks/use-auth'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { PackageCard, type PackageData, type PackageStatus } from '@/components/shipping/package-card'
import { 
  Package, 
  Plus, 
  Search, 
  Filter, 
  Download, 
  RefreshCw,
  Grid3X3,
  List,
  SortAsc,
  SortDesc,
  Calendar,
  MapPin,
  Truck
} from 'lucide-react'

// Filter and sort options
type SortOption = 'date-desc' | 'date-asc' | 'status' | 'destination' | 'weight'
type ViewMode = 'grid' | 'list'

interface PackageFilters {
  search: string
  status: PackageStatus | 'all'
  dateRange: 'all' | 'today' | 'week' | 'month'
  destination: string
}

export default function PackagesPage() {
  const { user } = useAuth()
  const searchParams = useSearchParams()

  // State management
  const [viewMode, setViewMode] = useState<ViewMode>('grid')
  const [sortBy, setSortBy] = useState<SortOption>('date-desc')
  const [isLoading, setIsLoading] = useState(false)
  const [selectedPackages, setSelectedPackages] = useState<string[]>([])
  const [showCreateDialog, setShowCreateDialog] = useState(false)

  // Filter state
  const [filters, setFilters] = useState<PackageFilters>({
    search: '',
    status: 'all',
    dateRange: 'all',
    destination: ''
  })

  // Handle URL parameters for filtering
  useEffect(() => {
    const statusParam = searchParams.get('status')
    if (statusParam) {
      setFilters(prev => ({
        ...prev,
        status: statusParam as PackageStatus | 'all'
      }))
    }
  }, [searchParams])

  // Mock package data - in real app, this would come from API
  const mockPackages: PackageData[] = [
    {
      id: 'pkg-001',
      trackingNumber: 'LBX-2025-001234',
      description: 'Electronics - iPhone 15 Pro Max',
      status: 'in-transit',
      destination: 'New York, NY, USA',
      weight: 1.2,
      dimensions: { length: 6, width: 3, height: 0.3 },
      createdAt: '2025-01-10',
      estimatedDelivery: '2025-01-15',
      carrier: 'DHL Express',
      value: 1299.99
    },
    {
      id: 'pkg-002',
      trackingNumber: 'LBX-2025-001235',
      description: 'Clothing - Winter Collection',
      status: 'processing',
      destination: 'Los Angeles, CA, USA',
      weight: 2.5,
      dimensions: { length: 12, width: 10, height: 4 },
      createdAt: '2025-01-09',
      estimatedDelivery: '2025-01-16',
      carrier: 'FedEx',
      value: 299.99
    },
    {
      id: 'pkg-003',
      trackingNumber: 'LBX-2025-001236',
      description: 'Books - Programming Collection',
      status: 'delivered',
      destination: 'Chicago, IL, USA',
      weight: 3.8,
      dimensions: { length: 10, width: 8, height: 6 },
      createdAt: '2025-01-08',
      estimatedDelivery: '2025-01-12',
      carrier: 'UPS',
      value: 149.99
    },
    {
      id: 'pkg-004',
      trackingNumber: 'LBX-2025-001237',
      description: 'Home & Garden - Kitchen Appliances',
      status: 'pending',
      destination: 'Miami, FL, USA',
      weight: 5.2,
      dimensions: { length: 15, width: 12, height: 8 },
      createdAt: '2025-01-11',
      estimatedDelivery: '2025-01-18',
      carrier: 'USPS',
      value: 89.99
    },
    {
      id: 'pkg-005',
      trackingNumber: 'LBX-2025-001238',
      description: 'Sports Equipment - Tennis Racket',
      status: 'exception',
      destination: 'Seattle, WA, USA',
      weight: 0.8,
      dimensions: { length: 28, width: 12, height: 2 },
      createdAt: '2025-01-07',
      estimatedDelivery: '2025-01-14',
      carrier: 'DHL Express',
      value: 199.99
    },
    {
      id: 'pkg-006',
      trackingNumber: 'LBX-2025-001239',
      description: 'Beauty & Personal Care',
      status: 'shipped',
      destination: 'Boston, MA, USA',
      weight: 1.5,
      dimensions: { length: 8, width: 6, height: 4 },
      createdAt: '2025-01-06',
      estimatedDelivery: '2025-01-13',
      carrier: 'FedEx',
      value: 79.99
    },
    {
      id: 'pkg-007',
      trackingNumber: 'LBX-2025-001240',
      description: 'Electronics - Laptop Accessories',
      status: 'in-account' as PackageStatus,
      destination: 'Denver, CO, USA',
      weight: 0.9,
      dimensions: { length: 15, width: 10, height: 3 },
      createdAt: '2025-01-12',
      estimatedDelivery: '2025-01-17',
      carrier: 'DHL Express',
      value: 159.99
    },
    {
      id: 'pkg-008',
      trackingNumber: 'LBX-2025-001241',
      description: 'Home Decor - Picture Frames',
      status: 'ready-for-mailout' as PackageStatus,
      destination: 'Phoenix, AZ, USA',
      weight: 2.1,
      dimensions: { length: 20, width: 15, height: 5 },
      createdAt: '2025-01-11',
      estimatedDelivery: '2025-01-16',
      carrier: 'Aramex',
      value: 89.99
    },
    {
      id: 'pkg-009',
      trackingNumber: 'LBX-2025-001242',
      description: 'Clothing - Summer Collection',
      status: 'sent' as PackageStatus,
      destination: 'Portland, OR, USA',
      weight: 1.7,
      dimensions: { length: 12, width: 8, height: 6 },
      createdAt: '2025-01-05',
      estimatedDelivery: '2025-01-10',
      carrier: 'FedEx',
      value: 129.99
    }
  ]

  /**
   * Filters and sorts packages based on current filters and sort options
   */
  const filteredAndSortedPackages = useMemo(() => {
    let filtered = mockPackages

    // Apply search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      filtered = filtered.filter(pkg => 
        pkg.trackingNumber.toLowerCase().includes(searchLower) ||
        pkg.description.toLowerCase().includes(searchLower) ||
        pkg.destination.toLowerCase().includes(searchLower)
      )
    }

    // Apply status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(pkg => pkg.status === filters.status)
    }

    // Apply destination filter
    if (filters.destination) {
      const destLower = filters.destination.toLowerCase()
      filtered = filtered.filter(pkg => 
        pkg.destination.toLowerCase().includes(destLower)
      )
    }

    // Apply date range filter
    if (filters.dateRange !== 'all') {
      const now = new Date()
      const filterDate = new Date()
      
      switch (filters.dateRange) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0)
          break
        case 'week':
          filterDate.setDate(now.getDate() - 7)
          break
        case 'month':
          filterDate.setMonth(now.getMonth() - 1)
          break
      }
      
      filtered = filtered.filter(pkg => 
        new Date(pkg.createdAt) >= filterDate
      )
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date-desc':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        case 'date-asc':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        case 'status':
          return a.status.localeCompare(b.status)
        case 'destination':
          return a.destination.localeCompare(b.destination)
        case 'weight':
          return b.weight - a.weight
        default:
          return 0
      }
    })

    return filtered
  }, [mockPackages, filters, sortBy])

  /**
   * Gets package statistics for display
   */
  const packageStats = useMemo(() => {
    const total = mockPackages.length
    const pending = mockPackages.filter(p => p.status === 'pending').length
    const processing = mockPackages.filter(p => p.status === 'processing').length
    const inTransit = mockPackages.filter(p => p.status === 'in-transit').length
    const delivered = mockPackages.filter(p => p.status === 'delivered').length
    const exceptions = mockPackages.filter(p => p.status === 'exception').length

    return { total, pending, processing, inTransit, delivered, exceptions }
  }, [mockPackages])

  /**
   * Handles package tracking
   */
  const handleTrackPackage = (trackingNumber: string) => {
    // TODO: Implement tracking functionality
    console.log('Tracking package:', trackingNumber)
  }

  /**
   * Handles package editing
   */
  const handleEditPackage = (packageId: string) => {
    // TODO: Navigate to package edit page
    console.log('Editing package:', packageId)
  }

  /**
   * Handles viewing package details
   */
  const handleViewPackageDetails = (packageId: string) => {
    // TODO: Navigate to package details page
    console.log('Viewing package details:', packageId)
  }

  /**
   * Handles package selection for bulk operations
   */
  const handlePackageSelect = (packageId: string, selected: boolean) => {
    if (selected) {
      setSelectedPackages(prev => [...prev, packageId])
    } else {
      setSelectedPackages(prev => prev.filter(id => id !== packageId))
    }
  }

  /**
   * Handles bulk operations
   */
  const handleBulkAction = (action: string) => {
    console.log(`Bulk action: ${action} on packages:`, selectedPackages)
    // TODO: Implement bulk operations
    setSelectedPackages([])
  }

  /**
   * Handles refresh
   */
  const handleRefresh = async () => {
    setIsLoading(true)
    // TODO: Implement data refresh
    setTimeout(() => setIsLoading(false), 1000)
  }

  /**
   * Handles export
   */
  const handleExport = () => {
    // TODO: Implement export functionality
    console.log('Exporting packages')
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Package Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Track and manage all your shipments in one place
            </p>
          </div>
          <div className="mt-4 md:mt-0 flex items-center space-x-2">
            <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button variant="outline" onClick={handleExport}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              New Package
            </Button>
          </div>
        </div>

        {/* Package Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Package className="h-4 w-4 text-blue-600" />
                <div>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{packageStats.total}</p>
                  <p className="text-xs text-gray-500">Total</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-gray-600" />
                <div>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{packageStats.pending}</p>
                  <p className="text-xs text-gray-500">Pending</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Package className="h-4 w-4 text-blue-600" />
                <div>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{packageStats.processing}</p>
                  <p className="text-xs text-gray-500">Processing</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Truck className="h-4 w-4 text-purple-600" />
                <div>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{packageStats.inTransit}</p>
                  <p className="text-xs text-gray-500">In Transit</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4 text-green-600" />
                <div>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{packageStats.delivered}</p>
                  <p className="text-xs text-gray-500">Delivered</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Package className="h-4 w-4 text-red-600" />
                <div>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{packageStats.exceptions}</p>
                  <p className="text-xs text-gray-500">Exceptions</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Filter className="h-5 w-5 text-blue-600" />
              <span>Filters & Search</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Search */}
              <div className="space-y-2">
                <Label htmlFor="search">Search Packages</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="search"
                    placeholder="Tracking number, description..."
                    value={filters.search}
                    onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Status Filter */}
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <select
                  id="status"
                  value={filters.status}
                  onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value as PackageStatus | 'all' }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800"
                >
                  <option value="all">All Status</option>
                  <option value="pending">Pending</option>
                  <option value="processing">Processing</option>
                  <option value="shipped">Shipped</option>
                  <option value="in-transit">In Transit</option>
                  <option value="delivered">Delivered</option>
                  <option value="exception">Exception</option>
                </select>
              </div>

              {/* Date Range Filter */}
              <div className="space-y-2">
                <Label htmlFor="dateRange">Date Range</Label>
                <select
                  id="dateRange"
                  value={filters.dateRange}
                  onChange={(e) => setFilters(prev => ({ ...prev, dateRange: e.target.value as any }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800"
                >
                  <option value="all">All Time</option>
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                </select>
              </div>

              {/* Destination Filter */}
              <div className="space-y-2">
                <Label htmlFor="destination">Destination</Label>
                <Input
                  id="destination"
                  placeholder="City, State, Country..."
                  value={filters.destination}
                  onChange={(e) => setFilters(prev => ({ ...prev, destination: e.target.value }))}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* View Controls and Bulk Actions */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div className="flex items-center space-x-4">
            {/* View Mode Toggle */}
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>

            {/* Sort Options */}
            <div className="flex items-center space-x-2">
              <Label htmlFor="sort" className="text-sm">Sort by:</Label>
              <select
                id="sort"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as SortOption)}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800"
              >
                <option value="date-desc">Newest First</option>
                <option value="date-asc">Oldest First</option>
                <option value="status">Status</option>
                <option value="destination">Destination</option>
                <option value="weight">Weight</option>
              </select>
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedPackages.length > 0 && (
            <div className="mt-4 md:mt-0 flex items-center space-x-2">
              <Badge variant="secondary">
                {selectedPackages.length} selected
              </Badge>
              <Button variant="outline" size="sm" onClick={() => handleBulkAction('export')}>
                Export Selected
              </Button>
              <Button variant="outline" size="sm" onClick={() => handleBulkAction('update-status')}>
                Update Status
              </Button>
              <Button variant="outline" size="sm" onClick={() => setSelectedPackages([])}>
                Clear Selection
              </Button>
            </div>
          )}
        </div>

        {/* Package List */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Packages ({filteredAndSortedPackages.length})
            </h2>
          </div>

          {filteredAndSortedPackages.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  No packages found
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {filters.search || filters.status !== 'all' || filters.destination
                    ? 'Try adjusting your filters to see more packages.'
                    : 'Create your first package to get started.'}
                </p>
                <Button onClick={() => setShowCreateDialog(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Package
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className={
              viewMode === 'grid'
                ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'
                : 'space-y-4'
            }>
              {filteredAndSortedPackages.map((pkg) => (
                <PackageCard
                  key={pkg.id}
                  package={pkg}
                  onTrack={handleTrackPackage}
                  onEdit={handleEditPackage}
                  onViewDetails={handleViewPackageDetails}
                  className={viewMode === 'list' ? 'w-full' : ''}
                />
              ))}
            </div>
          )}
        </div>

        {/* TODO: Add CreatePackageDialog component */}
        {showCreateDialog && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <Card className="w-full max-w-md">
              <CardHeader>
                <CardTitle>Create New Package</CardTitle>
                <CardDescription>
                  Package creation dialog will be implemented in the next phase
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button onClick={() => setShowCreateDialog(false)} className="w-full">
                  Close
                </Button>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
