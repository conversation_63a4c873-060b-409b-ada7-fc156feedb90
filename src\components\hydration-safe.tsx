'use client'

import { useEffect, useState } from 'react'

interface HydrationSafeProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

/**
 * Hydration Safe Component
 * 
 * Prevents hydration mismatches by only rendering children on the client side
 * after the component has mounted. This is useful for components that might
 * be affected by browser extensions or other client-side modifications.
 */
export default function HydrationSafe({ children, fallback = null }: HydrationSafeProps) {
  const [hasMounted, setHasMounted] = useState(false)

  useEffect(() => {
    setHasMounted(true)
  }, [])

  if (!hasMounted) {
    return <>{fallback}</>
  }

  return <>{children}</>
}
