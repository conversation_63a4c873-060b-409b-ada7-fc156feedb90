/**
 * PackageCard Component
 * 
 * Displays package information in a card format for the shipping portal
 * 
 * Features:
 * - Package status with color-coded badges
 * - Package details (tracking number, destination, weight)
 * - Action buttons (track, edit, view details)
 * - Responsive design
 * - Loading and error states
 * - Accessibility support
 * 
 * @param package - Package data object
 * @param onTrack - Callback when track button is clicked
 * @param onEdit - Callback when edit button is clicked
 * @param onViewDetails - Callback when view details is clicked
 * @param isLoading - Loading state for the card
 */

import { Package, MapPin, Weight, Calendar, ExternalLink, Edit, Eye } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

// Package status type definition
export type PackageStatus =
  | "pending"
  | "processing"
  | "shipped"
  | "in-transit"
  | "delivered"
  | "exception"
  | "in-account"
  | "ready-for-mailout"
  | "sent"
  | "cancelled"

// Package data interface
export interface PackageData {
  id: string
  trackingNumber: string
  description: string
  status: PackageStatus
  destination: string
  weight: number
  dimensions?: {
    length: number
    width: number
    height: number
  }
  createdAt: string
  estimatedDelivery?: string
  carrier?: string
  value?: number
}

interface PackageCardProps {
  package: PackageData
  onTrack?: (trackingNumber: string) => void
  onEdit?: (packageId: string) => void
  onViewDetails?: (packageId: string) => void
  isLoading?: boolean
  className?: string
}

/**
 * Returns appropriate styling for package status
 */
const getStatusConfig = (status: PackageStatus) => {
  const configs = {
    pending: {
      label: "Pending",
      variant: "secondary" as const,
      className: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200"
    },
    processing: {
      label: "Processing",
      variant: "default" as const,
      className: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
    },
    shipped: {
      label: "Shipped",
      variant: "default" as const,
      className: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
    },
    "in-transit": {
      label: "In Transit",
      variant: "default" as const,
      className: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
    },
    delivered: {
      label: "Delivered",
      variant: "default" as const,
      className: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
    },
    exception: {
      label: "Exception",
      variant: "destructive" as const,
      className: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
    },
    "in-account": {
      label: "In Account",
      variant: "default" as const,
      className: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
    },
    "ready-for-mailout": {
      label: "Ready for Mailout",
      variant: "default" as const,
      className: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
    },
    sent: {
      label: "Sent",
      variant: "default" as const,
      className: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
    },
    cancelled: {
      label: "Cancelled",
      variant: "destructive" as const,
      className: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
    }
  }
  
  return configs[status] || configs.pending
}

/**
 * Formats weight for display
 */
const formatWeight = (weight: number): string => {
  return `${weight} lbs`
}

/**
 * Formats date for display
 */
const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
}

export function PackageCard({
  package: pkg,
  onTrack,
  onEdit,
  onViewDetails,
  isLoading = false,
  className
}: PackageCardProps) {
  const statusConfig = getStatusConfig(pkg.status)

  if (isLoading) {
    return (
      <Card className={cn("animate-pulse", className)}>
        <CardHeader>
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="h-3 bg-gray-200 rounded"></div>
            <div className="h-3 bg-gray-200 rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn("hover:shadow-md transition-shadow", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2">
            <Package className="h-5 w-5 text-blue-600" />
            <CardTitle className="text-lg font-semibold">
              {pkg.trackingNumber}
            </CardTitle>
          </div>
          <Badge 
            variant={statusConfig.variant}
            className={statusConfig.className}
          >
            {statusConfig.label}
          </Badge>
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
          {pkg.description}
        </p>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-3">
          {/* Package Details */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4 text-gray-500" />
              <span className="text-gray-600 dark:text-gray-300">
                {pkg.destination}
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              <Weight className="h-4 w-4 text-gray-500" />
              <span className="text-gray-600 dark:text-gray-300">
                {formatWeight(pkg.weight)}
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="text-gray-600 dark:text-gray-300">
                Created: {formatDate(pkg.createdAt)}
              </span>
            </div>
            
            {pkg.estimatedDelivery && (
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="text-gray-600 dark:text-gray-300">
                  ETA: {formatDate(pkg.estimatedDelivery)}
                </span>
              </div>
            )}
          </div>

          {/* Carrier Info */}
          {pkg.carrier && (
            <div className="text-sm">
              <span className="text-gray-500">Carrier: </span>
              <span className="font-medium text-gray-900 dark:text-gray-100">
                {pkg.carrier}
              </span>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2 pt-2">
            {onTrack && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onTrack(pkg.trackingNumber)}
                className="flex items-center space-x-1"
              >
                <ExternalLink className="h-3 w-3" />
                <span>Track</span>
              </Button>
            )}
            
            {onViewDetails && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onViewDetails(pkg.id)}
                className="flex items-center space-x-1"
              >
                <Eye className="h-3 w-3" />
                <span>Details</span>
              </Button>
            )}
            
            {onEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(pkg.id)}
                className="flex items-center space-x-1"
              >
                <Edit className="h-3 w-3" />
                <span>Edit</span>
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
