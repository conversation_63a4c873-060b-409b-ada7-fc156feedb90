/**
 * Package Consolidations Page - LibyanoEx Forwarding Service
 *
 * Manages package consolidation requests and operations
 * Allows customers to combine multiple packages into single shipments
 * for cost savings and efficiency
 */

'use client'

import { useState } from 'react'
import { useAuth } from '@/hooks/use-auth'
import RoleBasedDashboardLayout from '@/components/layout/role-based-dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Combine, 
  Package, 
  Plus, 
  Calendar, 
  Weight, 
  DollarSign,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react'

// Mock consolidation data
const mockConsolidations = [
  {
    id: 'CONS-001',
    status: 'pending',
    packageCount: 3,
    totalWeight: 2.5,
    estimatedSavings: 25.50,
    createdAt: '2025-01-10',
    estimatedCompletion: '2025-01-15',
    packages: ['PKG-001', 'PKG-002', 'PKG-003']
  },
  {
    id: 'CONS-002',
    status: 'processing',
    packageCount: 2,
    totalWeight: 1.8,
    estimatedSavings: 15.75,
    createdAt: '2025-01-08',
    estimatedCompletion: '2025-01-12',
    packages: ['PKG-004', 'PKG-005']
  }
]

export default function ConsolidationsPage() {
  const { user } = useAuth()
  const [consolidations] = useState(mockConsolidations)

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'processing': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="h-4 w-4" />
      case 'processing': return <AlertCircle className="h-4 w-4" />
      case 'completed': return <CheckCircle className="h-4 w-4" />
      default: return <Package className="h-4 w-4" />
    }
  }

  return (
    <RoleBasedDashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Package Consolidations
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Combine multiple packages to save on shipping costs
            </p>
          </div>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Consolidation
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Combine className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Active Consolidations
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {consolidations.length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Estimated Savings
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    ${consolidations.reduce((sum, c) => sum + c.estimatedSavings, 0).toFixed(2)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Package className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Packages in Queue
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {consolidations.reduce((sum, c) => sum + c.packageCount, 0)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Consolidations List */}
        <Card>
          <CardHeader>
            <CardTitle>Your Consolidations</CardTitle>
            <CardDescription>
              Track the status of your package consolidation requests
            </CardDescription>
          </CardHeader>
          <CardContent>
            {consolidations.length === 0 ? (
              <div className="text-center py-12">
                <Combine className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  No consolidations yet
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Create your first consolidation to start saving on shipping costs
                </p>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Consolidation
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {consolidations.map((consolidation) => (
                  <div
                    key={consolidation.id}
                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                          {consolidation.id}
                        </h3>
                        <Badge className={getStatusColor(consolidation.status)}>
                          {getStatusIcon(consolidation.status)}
                          <span className="ml-1 capitalize">{consolidation.status}</span>
                        </Badge>
                      </div>
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600 dark:text-gray-400">Packages</p>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {consolidation.packageCount} items
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-600 dark:text-gray-400">Total Weight</p>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {consolidation.totalWeight} kg
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-600 dark:text-gray-400">Estimated Savings</p>
                        <p className="font-medium text-green-600">
                          ${consolidation.estimatedSavings}
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-600 dark:text-gray-400">Est. Completion</p>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {new Date(consolidation.estimatedCompletion).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </RoleBasedDashboardLayout>
  )
}
