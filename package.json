{"name": "libyanoex-web-app", "version": "0.1.0", "private": true, "description": "LibyanoEx Web Application System - A comprehensive business management platform", "scripts": {"dev": "next dev -p 3000", "dev:alt": "next dev -p 3001", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-slot": "^1.0.2", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "lucide-react": "^0.525.0", "next": "15.3.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.48.2", "tailwind-merge": "^2.2.1", "zod": "^3.22.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}