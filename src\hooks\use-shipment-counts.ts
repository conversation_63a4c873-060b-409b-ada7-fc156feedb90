'use client'

import { useState, useEffect } from 'react'

interface ShipmentCounts {
  total: number
  underReview: number
  approved: number
  readyToSend: number
  completed: number
  cancelled: number
}

/**
 * Hook to get shipment counts for different statuses
 * 
 * This would typically fetch from an API, but for now uses mock data
 * that simulates real-time shipment counts for Door-to-Door service
 */
export function useShipmentCounts(): ShipmentCounts {
  const [counts, setCounts] = useState<ShipmentCounts>({
    total: 0,
    underReview: 0,
    approved: 0,
    readyToSend: 0,
    completed: 0,
    cancelled: 0
  })

  useEffect(() => {
    // Mock data - in real app, this would be an API call
    const mockCounts: ShipmentCounts = {
      total: 18,
      underReview: 3,
      approved: 2,
      readyToSend: 1,
      completed: 11,
      cancelled: 1
    }

    // Simulate API delay
    const timer = setTimeout(() => {
      setCounts(mockCounts)
    }, 100)

    return () => clearTimeout(timer)
  }, [])

  return counts
}

/**
 * Hook to get shipments by status
 * 
 * This would filter shipments based on their status
 * For now, returns mock data
 */
export function useShipmentsByStatus(status: string) {
  const [shipments, setShipments] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Mock implementation - would fetch filtered shipments from API
    setIsLoading(true)
    
    const timer = setTimeout(() => {
      // Mock filtered shipments based on status
      const mockShipments = []
      setShipments(mockShipments)
      setIsLoading(false)
    }, 500)

    return () => clearTimeout(timer)
  }, [status])

  return { shipments, isLoading }
}
