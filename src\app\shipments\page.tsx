/**
 * Shipments Overview Page - LibyanoEx Door-to-Door Service
 *
 * Displays all customer shipments with filtering and status management
 * Supports Door-to-Door service from US, Canada, UK to Libya via DHL
 */

'use client'

import { useState, useMemo, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { useAuth } from '@/hooks/use-auth'
import RoleBasedDashboardLayout from '@/components/layout/role-based-dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Send, 
  Plus, 
  Search, 
  Filter, 
  Calendar, 
  MapPin,
  Package2,
  DollarSign,
  Clock,
  CheckCircle,
  Truck,
  XCircle,
  Eye,
  Download
} from 'lucide-react'

// Shipment status type
type ShipmentStatus = 'under-review' | 'approved' | 'ready-to-send' | 'completed' | 'cancelled'

// Shipment data interface
interface ShipmentData {
  id: string
  trackingNumber: string
  senderName: string
  recipientName: string
  recipientCity: string
  status: ShipmentStatus
  totalPackages: number
  totalWeight: number
  declaredValue: number
  createdAt: string
  estimatedDelivery?: string
  dhlTrackingNumber?: string
}

// Mock shipments data
const mockShipments: ShipmentData[] = [
  {
    id: 'ship-001',
    trackingNumber: 'LBX-SHIP-2025-001',
    senderName: 'John Smith',
    recipientName: 'Ahmed Al-Mansouri',
    recipientCity: 'Tripoli',
    status: 'under-review',
    totalPackages: 2,
    totalWeight: 3.5,
    declaredValue: 299.99,
    createdAt: '2025-01-15',
    estimatedDelivery: '2025-01-22'
  },
  {
    id: 'ship-002',
    trackingNumber: 'LBX-SHIP-2025-002',
    senderName: 'Sarah Johnson',
    recipientName: 'Fatima Benali',
    recipientCity: 'Benghazi',
    status: 'approved',
    totalPackages: 1,
    totalWeight: 1.8,
    declaredValue: 159.99,
    createdAt: '2025-01-14',
    estimatedDelivery: '2025-01-21'
  },
  {
    id: 'ship-003',
    trackingNumber: 'LBX-SHIP-2025-003',
    senderName: 'Michael Brown',
    recipientName: 'Omar Khalil',
    recipientCity: 'Misrata',
    status: 'ready-to-send',
    totalPackages: 3,
    totalWeight: 5.2,
    declaredValue: 449.99,
    createdAt: '2025-01-13',
    estimatedDelivery: '2025-01-20',
    dhlTrackingNumber: 'DHL123456789'
  }
]

export default function ShipmentsPage() {
  const { user } = useAuth()
  const searchParams = useSearchParams()
  
  // State management
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<ShipmentStatus | 'all'>('all')

  // Handle URL parameters for filtering
  useEffect(() => {
    const statusParam = searchParams.get('status')
    if (statusParam) {
      setStatusFilter(statusParam as ShipmentStatus | 'all')
    }
  }, [searchParams])

  // Filter and search shipments
  const filteredShipments = useMemo(() => {
    let filtered = mockShipments

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase()
      filtered = filtered.filter(shipment => 
        shipment.trackingNumber.toLowerCase().includes(searchLower) ||
        shipment.senderName.toLowerCase().includes(searchLower) ||
        shipment.recipientName.toLowerCase().includes(searchLower) ||
        shipment.recipientCity.toLowerCase().includes(searchLower)
      )
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(shipment => shipment.status === statusFilter)
    }

    return filtered
  }, [searchTerm, statusFilter])

  const getStatusConfig = (status: ShipmentStatus) => {
    const configs = {
      'under-review': {
        label: 'Under Review',
        className: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
        icon: Clock
      },
      'approved': {
        label: 'Approved',
        className: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
        icon: CheckCircle
      },
      'ready-to-send': {
        label: 'Ready to Send',
        className: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
        icon: Truck
      },
      'completed': {
        label: 'Completed',
        className: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        icon: Package2
      },
      'cancelled': {
        label: 'Cancelled',
        className: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
        icon: XCircle
      }
    }
    return configs[status]
  }

  // Calculate summary stats
  const stats = useMemo(() => {
    return {
      total: mockShipments.length,
      underReview: mockShipments.filter(s => s.status === 'under-review').length,
      approved: mockShipments.filter(s => s.status === 'approved').length,
      readyToSend: mockShipments.filter(s => s.status === 'ready-to-send').length,
      completed: mockShipments.filter(s => s.status === 'completed').length,
      totalValue: mockShipments.reduce((sum, s) => sum + s.declaredValue, 0)
    }
  }, [])

  return (
    <RoleBasedDashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Door-to-Door Shipments
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage your shipments from US, Canada, UK to Libya via DHL
            </p>
          </div>
          <Link href="/shipments/request">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Request New Shipment
            </Button>
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Send className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Shipments
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.total}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Under Review
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.underReview}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Truck className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Ready to Send
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.readyToSend}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Total Value
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    ${stats.totalValue.toFixed(2)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search shipments..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant={statusFilter === 'all' ? 'default' : 'outline'}
                  onClick={() => setStatusFilter('all')}
                  size="sm"
                >
                  All
                </Button>
                <Button
                  variant={statusFilter === 'under-review' ? 'default' : 'outline'}
                  onClick={() => setStatusFilter('under-review')}
                  size="sm"
                >
                  Under Review
                </Button>
                <Button
                  variant={statusFilter === 'approved' ? 'default' : 'outline'}
                  onClick={() => setStatusFilter('approved')}
                  size="sm"
                >
                  Approved
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Shipments List */}
        <Card>
          <CardHeader>
            <CardTitle>Your Shipments</CardTitle>
            <CardDescription>
              Track and manage your Door-to-Door shipment requests
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredShipments.length === 0 ? (
              <div className="text-center py-12">
                <Send className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  No shipments found
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {searchTerm || statusFilter !== 'all' 
                    ? 'Try adjusting your search or filters'
                    : 'Create your first Door-to-Door shipment request'
                  }
                </p>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Request Shipment
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredShipments.map((shipment) => {
                  const statusConfig = getStatusConfig(shipment.status)
                  const StatusIcon = statusConfig.icon
                  
                  return (
                    <div
                      key={shipment.id}
                      className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            {shipment.trackingNumber}
                          </h3>
                          <Badge className={statusConfig.className}>
                            <StatusIcon className="h-3 w-3 mr-1" />
                            {statusConfig.label}
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </Button>
                          {shipment.dhlTrackingNumber && (
                            <Button variant="outline" size="sm">
                              <Download className="h-4 w-4 mr-2" />
                              Label
                            </Button>
                          )}
                        </div>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                        <div>
                          <p className="text-gray-600 dark:text-gray-400">From</p>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {shipment.senderName}
                          </p>
                        </div>
                        <div>
                          <p className="text-gray-600 dark:text-gray-400">To</p>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {shipment.recipientName}
                          </p>
                          <p className="text-xs text-gray-500">{shipment.recipientCity}</p>
                        </div>
                        <div>
                          <p className="text-gray-600 dark:text-gray-400">Packages</p>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {shipment.totalPackages} ({shipment.totalWeight} kg)
                          </p>
                        </div>
                        <div>
                          <p className="text-gray-600 dark:text-gray-400">Value</p>
                          <p className="font-medium text-gray-900 dark:text-white">
                            ${shipment.declaredValue}
                          </p>
                        </div>
                        <div>
                          <p className="text-gray-600 dark:text-gray-400">Created</p>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {new Date(shipment.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </RoleBasedDashboardLayout>
  )
}
